import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';

import '../../professional_reports/models/report_template_models.dart';
import '../../controllers/report_template_controller.dart';
import '../../services/api/report_templates_api_service.dart';

/// شاشة معاينة قالب التقرير
class TemplatePreviewScreen extends StatefulWidget {
  final ReportTemplate template;

  const TemplatePreviewScreen({
    super.key,
    required this.template,
  });

  @override
  State<TemplatePreviewScreen> createState() => _TemplatePreviewScreenState();
}

class _TemplatePreviewScreenState extends State<TemplatePreviewScreen> {
  final ReportTemplatesApiService _apiService = ReportTemplatesApiService();
  final ReportTemplateController _templateController = Get.find<ReportTemplateController>();
  
  bool _isLoading = false;
  Map<String, dynamic>? _previewData;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPreview();
  }

  Future<void> _loadPreview() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل معاينة القالب مع بيانات عينة
      _previewData = await _apiService.previewTemplate(widget.template.id, null);
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('معاينة: ${widget.template.name}'),
        backgroundColor: widget.template.designSettings.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editTemplate,
            tooltip: 'تعديل القالب',
          ),
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _duplicateTemplate,
            tooltip: 'نسخ القالب',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.file_download),
                  title: Text('تصدير القالب'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'apply',
                child: ListTile(
                  leading: Icon(Icons.apple_sharp),
                  title: Text('تطبيق على تقرير'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'validate',
                child: ListTile(
                  leading: Icon(Icons.check_circle),
                  title: Text('التحقق من صحة القالب'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _useTemplate,
        icon: const Icon(Icons.play_arrow),
        label: const Text('استخدام القالب'),
        backgroundColor: widget.template.designSettings.primaryColor,
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل المعاينة...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل المعاينة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPreview,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTemplateInfo(),
          const SizedBox(height: 24),
          _buildDesignSettings(),
          const SizedBox(height: 24),
          _buildContentSettings(),
          const SizedBox(height: 24),
          _buildExportSettings(),
          const SizedBox(height: 24),
          _buildPreviewSection(),
        ],
      ),
    );
  }

  Widget _buildTemplateInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  widget.template.reportType.icon,
                  color: widget.template.designSettings.primaryColor,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.template.name,
                        style: AppStyles.titleLarge.copyWith(
                          color: widget.template.designSettings.primaryColor,
                        ),
                      ),
                      Text(
                        widget.template.reportType.displayName,
                        style: AppStyles.bodyMedium.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.template.isDefault)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'افتراضي',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                if (widget.template.isCustom)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'مخصص',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              widget.template.description,
              style: AppStyles.bodyMedium,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'تم الإنشاء: ${_formatDate(widget.template.createdAt)}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                if (widget.template.updatedAt != null) ...[
                  const SizedBox(width: 16),
                  Icon(Icons.edit, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'آخر تحديث: ${_formatDate(widget.template.updatedAt!)}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesignSettings() {
    final settings = widget.template.designSettings;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('إعدادات التصميم', style: AppStyles.titleMedium),
            const Divider(),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildColorSample('اللون الأساسي', settings.primaryColor),
                const SizedBox(width: 16),
                _buildColorSample('اللون الثانوي', settings.secondaryColor),
                const SizedBox(width: 16),
                _buildColorSample('لون الخلفية', settings.backgroundColor),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.font_download, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text('الخط: ${settings.bodyFont}'),
                const SizedBox(width: 16),
                Icon(Icons.format_size, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text('الحجم: ${settings.bodyFontSize}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorSample(String label, Color color) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildContentSettings() {
    final settings = widget.template.contentSettings;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('إعدادات المحتوى', style: AppStyles.titleMedium),
            const Divider(),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildSettingChip('العنوان', settings.showTitle),
                _buildSettingChip('التاريخ', settings.showDate),
                _buildSettingChip('الملخص', settings.showSummary),
                _buildSettingChip('الرسوم البيانية', settings.showCharts),
                _buildSettingChip('الجداول', settings.showTables),
                _buildSettingChip('التفاصيل', settings.showDetails),
                _buildSettingChip('التوقيع', settings.showSignature),
              ],
            ),
            if (settings.chartTypes.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text('أنواع الرسوم البيانية:', style: AppStyles.bodyMedium),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: settings.chartTypes.map((type) => Chip(
                  label: Text(type.displayName),
                  avatar: Icon(type.icon, size: 16),
                )).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSettingChip(String label, bool enabled) {
    return Chip(
      label: Text(label),
      backgroundColor: enabled ? Colors.green.shade100 : Colors.grey.shade200,
      labelStyle: TextStyle(
        color: enabled ? Colors.green.shade700 : Colors.grey.shade600,
      ),
      avatar: Icon(
        enabled ? Icons.check : Icons.close,
        size: 16,
        color: enabled ? Colors.green.shade700 : Colors.grey.shade600,
      ),
    );
  }

  Widget _buildExportSettings() {
    final settings = widget.template.exportSettings;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('إعدادات التصدير', style: AppStyles.titleMedium),
            const Divider(),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(settings.defaultFormat.icon, color: settings.defaultFormat.color),
                const SizedBox(width: 8),
                Text('التنسيق الافتراضي: ${settings.defaultFormat.displayName}'),
              ],
            ),
            const SizedBox(height: 8),
            Text('التنسيقات المدعومة:'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: settings.supportedFormats.map((format) => Chip(
                label: Text(format.displayName),
                avatar: Icon(format.icon, size: 16, color: format.color),
              )).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('معاينة التقرير', style: AppStyles.titleMedium),
            const Divider(),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              height: 300,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _previewData != null
                  ? _buildPreviewContent()
                  : const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.preview, size: 48, color: Colors.grey),
                          SizedBox(height: 8),
                          Text('معاينة التقرير ستظهر هنا'),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewContent() {
    // هنا يمكن إضافة منطق عرض المعاينة الفعلية
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'عينة من التقرير',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.template.designSettings.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'هذه معاينة تقريبية لكيفية ظهور التقرير باستخدام هذا القالب.',
            style: TextStyle(color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          // يمكن إضافة المزيد من عناصر المعاينة هنا
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _editTemplate() {
    // TODO: فتح شاشة تعديل القالب
    Get.snackbar('قريباً', 'ميزة تعديل القوالب قيد التطوير');
  }

  void _duplicateTemplate() async {
    final result = await Get.dialog<String>(
      AlertDialog(
        title: const Text('نسخ القالب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل اسم القالب الجديد:'),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'اسم القالب',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                // يمكن حفظ القيمة هنا
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: 'نسخة من ${widget.template.name}'),
            child: const Text('نسخ'),
          ),
        ],
      ),
    );

    if (result != null) {
      try {
        await _templateController.duplicateTemplate(widget.template.id, result);
        Get.snackbar('نجح', 'تم نسخ القالب بنجاح');
      } catch (e) {
        Get.snackbar('خطأ', 'فشل في نسخ القالب: $e');
      }
    }
  }

  void _useTemplate() {
    // TODO: استخدام القالب لإنشاء تقرير جديد
    Get.snackbar('قريباً', 'ميزة استخدام القوالب قيد التطوير');
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportTemplate();
        break;
      case 'apply':
        _applyToReport();
        break;
      case 'validate':
        _validateTemplate();
        break;
    }
  }

  void _exportTemplate() {
    // TODO: تصدير القالب
    Get.snackbar('قريباً', 'ميزة تصدير القوالب قيد التطوير');
  }

  void _applyToReport() {
    // TODO: تطبيق القالب على تقرير موجود
    Get.snackbar('قريباً', 'ميزة تطبيق القوالب قيد التطوير');
  }

  void _validateTemplate() async {
    try {
      final result = await _apiService.validateTemplate(widget.template);
      final isValid = result['isValid'] as bool? ?? false;
      final errors = result['errors'] as List<dynamic>? ?? [];

      Get.dialog(
        AlertDialog(
          title: Text(isValid ? 'القالب صحيح' : 'القالب يحتوي على أخطاء'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isValid)
                const Text('القالب صحيح ويمكن استخدامه بأمان.')
              else ...[
                const Text('تم العثور على الأخطاء التالية:'),
                const SizedBox(height: 8),
                ...errors.map((error) => Text('• $error')),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في التحقق من صحة القالب: $e');
    }
  }
}
