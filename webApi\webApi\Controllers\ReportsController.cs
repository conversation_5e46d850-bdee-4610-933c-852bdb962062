using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Services;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة التقارير
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class ReportsController(TasksDbContext context, ILoggingService loggingService) : ControllerBase
    {
        private readonly TasksDbContext _context = context;
        private readonly ILoggingService _loggingService = loggingService;

        /// <summary>
        /// الحصول على جميع التقارير
        /// </summary>
        /// <returns>قائمة بجميع التقارير</returns>
        /// <response code="200">إرجاع قائمة التقارير</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Report>>> GetReports()
        {
            return await _context.Reports
                //.Include(r => r.CreatedByNavigation)
                .Where(r => !r.IsDeleted)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على تقرير محدد
        /// </summary>
        /// <param name="id">معرف التقرير</param>
        /// <returns>التقرير المطلوب</returns>
        /// <response code="200">إرجاع التقرير</response>
        /// <response code="404">التقرير غير موجود</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Report>> GetReport(int id)
        {
            var report = await _context.Reports
                //.Include(r => r.CreatedByNavigation)
                .Include(r => r.ReportSchedules)
                .FirstOrDefaultAsync(r => r.Id == id && !r.IsDeleted);

            if (report == null)
            {
                return NotFound();
            }

            return report;
        }

        /// <summary>
        /// الحصول على التقارير العامة
        /// </summary>
        /// <returns>قائمة التقارير العامة</returns>
        /// <response code="200">إرجاع قائمة التقارير العامة</response>
        [HttpGet("public")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Report>>> GetPublicReports()
        {
            return await _context.Reports
                //.Include(r => r.CreatedByNavigation)
                .Where(r => r.IsPublic && !r.IsDeleted)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على تقارير مستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة تقارير المستخدم</returns>
        /// <response code="200">إرجاع قائمة التقارير</response>
        [HttpGet("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Report>>> GetUserReports(int userId)
        {
            return await _context.Reports
                .Where(r => r.CreatedBy == userId && !r.IsDeleted)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على التقارير حسب النوع
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>قائمة التقارير من النوع المحدد</returns>
        /// <response code="200">إرجاع قائمة التقارير</response>
        [HttpGet("type/{reportType}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Report>>> GetReportsByType(string reportType)
        {
            return await _context.Reports
                //.Include(r => r.CreatedByNavigation)
                .Where(r => r.ReportType == reportType && !r.IsDeleted)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// تقرير إحصائيات المهام
        /// </summary>
        /// <returns>إحصائيات المهام</returns>
        /// <response code="200">إرجاع إحصائيات المهام</response>
        [HttpGet("tasks-statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetTasksStatistics()
        {
            var totalTasks = await _context.Tasks.CountAsync(t => !t.IsDeleted);
            var completedTasks = await _context.Tasks.CountAsync(t => !t.IsDeleted && t.CompletedAt.HasValue);
            var pendingTasks = totalTasks - completedTasks;

            var tasksByStatus = await _context.Tasks
                .Where(t => !t.IsDeleted)
                .GroupBy(t => t.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var tasksByPriority = await _context.Tasks
                .Where(t => !t.IsDeleted)
                .GroupBy(t => t.Priority)
                .Select(g => new { Priority = g.Key, Count = g.Count() })
                .ToListAsync();

            var tasksByDepartment = await _context.Tasks
                .Include(t => t.Department)
                .Where(t => !t.IsDeleted && t.Department != null)
                .GroupBy(t => t.Department!.Name)
                .Select(g => new { Department = g.Key, Count = g.Count() })
                .ToListAsync();

            return Ok(new
            {
                TotalTasks = totalTasks,
                CompletedTasks = completedTasks,
                PendingTasks = pendingTasks,
                CompletionRate = totalTasks > 0 ? Math.Round((double)completedTasks / totalTasks * 100, 2) : 0,
                TasksByStatus = tasksByStatus,
                TasksByPriority = tasksByPriority,
                TasksByDepartment = tasksByDepartment
            });
        }

        /// <summary>
        /// تقرير إحصائيات المستخدمين
        /// </summary>
        /// <returns>إحصائيات المستخدمين</returns>
        /// <response code="200">إرجاع إحصائيات المستخدمين</response>
        [HttpGet("users-statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetUsersStatistics()
        {
            var totalUsers = await _context.Users.CountAsync(u => !u.IsDeleted);
            var activeUsers = await _context.Users.CountAsync(u => u.IsActive && !u.IsDeleted);
            var onlineUsers = await _context.Users.CountAsync(u => u.IsOnline && !u.IsDeleted);

            var usersByDepartment = await _context.Users
                .Include(u => u.Department)
                .Where(u => !u.IsDeleted && u.Department != null)
                .GroupBy(u => u.Department!.Name)
                .Select(g => new { Department = g.Key, Count = g.Count() })
                .ToListAsync();

            var usersByRole = await _context.Users
                .Where(u => !u.IsDeleted)
                .GroupBy(u => u.RoleId)
                .Select(g => new { RoleId = g.Key, Count = g.Count() })
                .ToListAsync();

            return Ok(new
            {
                TotalUsers = totalUsers,
                ActiveUsers = activeUsers,
                OnlineUsers = onlineUsers,
                UsersByDepartment = usersByDepartment,
                UsersByRole = usersByRole
            });
        }

        /// <summary>
        /// تقرير أداء المستخدمين
        /// </summary>
        /// <param name="startDate">تاريخ البداية (Unix timestamp)</param>
        /// <param name="endDate">تاريخ النهاية (Unix timestamp)</param>
        /// <returns>تقرير أداء المستخدمين</returns>
        /// <response code="200">إرجاع تقرير الأداء</response>
        [HttpGet("user-performance")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetUserPerformanceReport(
            [FromQuery] long? startDate = null, 
            [FromQuery] long? endDate = null)
        {
            var query = _context.Tasks.Include(t => t.Assignee).Where(t => !t.IsDeleted);

            if (startDate.HasValue)
                query = query.Where(t => t.CreatedAt >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(t => t.CreatedAt <= endDate.Value);

            var userPerformance = await query
                .Where(t => t.Assignee != null)
                .GroupBy(t => new { t.AssigneeId, t.Assignee!.Name })
                .Select(g => new
                {
                    UserId = g.Key.AssigneeId,
                    UserName = g.Key.Name,
                    TotalTasks = g.Count(),
                    CompletedTasks = g.Count(t => t.CompletedAt.HasValue),
                    PendingTasks = g.Count(t => !t.CompletedAt.HasValue),
                    CompletionRate = g.Count() > 0 ? Math.Round((double)g.Count(t => t.CompletedAt.HasValue) / g.Count() * 100, 2) : 0
                })
                .OrderByDescending(x => x.CompletionRate)
                .ToListAsync();

            return Ok(userPerformance);
        }

        /// <summary>
        /// إنشاء تقرير جديد
        /// </summary>
        /// <param name="report">بيانات التقرير</param>
        /// <returns>التقرير المُنشأ</returns>
        /// <response code="201">تم إنشاء التقرير بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<Report>> PostReport(Report report)
        {
            report.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            report.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            report.IsDeleted = false;

            _context.Reports.Add(report);
            await _context.SaveChangesAsync();

            // تسجيل إنشاء التقرير
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "create_report",
                    "report",
                    report.Id,
                    currentUserId,
                    $"إنشاء تقرير: {report.Title}");
            }

            return CreatedAtAction("GetReport", new { id = report.Id }, report);
        }

        /// <summary>
        /// تحديث تقرير
        /// </summary>
        /// <param name="id">معرف التقرير</param>
        /// <param name="report">بيانات التقرير المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث التقرير بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">التقرير غير موجود</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutReport(int id, Report report)
        {
            if (id != report.Id)
            {
                return BadRequest();
            }

            report.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            _context.Entry(report).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ReportExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// حذف تقرير (حذف منطقي)
        /// </summary>
        /// <param name="id">معرف التقرير</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف التقرير بنجاح</response>
        /// <response code="404">التقرير غير موجود</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteReport(int id)
        {
            var report = await _context.Reports.FindAsync(id);
            if (report == null || report.IsDeleted)
            {
                return NotFound();
            }

            // حذف منطقي
            report.IsDeleted = true;
            report.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            
            await _context.SaveChangesAsync();

            // تسجيل حذف التقرير
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "delete_report",
                    "report",
                    id,
                    currentUserId,
                    $"حذف تقرير: {report.Title}");
            }

            return NoContent();
        }

        /// <summary>
        /// تصدير تقرير بتنسيق محدد
        /// </summary>
        /// <param name="id">معرف التقرير</param>
        /// <param name="request">طلب التصدير</param>
        /// <returns>مسار الملف المصدر</returns>
        /// <response code="200">تم التصدير بنجاح</response>
        /// <response code="404">التقرير غير موجود</response>
        [HttpPost("{id}/export")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<object>> ExportReport(int id, [FromBody] ExportReportRequest request)
        {
            var report = await _context.Reports.FindAsync(id);
            if (report == null || report.IsDeleted)
            {
                return NotFound();
            }

            try
            {
                // هنا يمكن إضافة منطق التصدير الفعلي
                var fileName = $"report_{id}_{DateTime.Now:yyyyMMdd_HHmmss}.{request.Format}";
                var filePath = Path.Combine("exports", fileName);

                // تسجيل عملية التصدير
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "export_report",
                        "report",
                        id,
                        currentUserId,
                        $"تصدير تقرير: {report.Title} بتنسيق {request.Format}");
                }

                return Ok(new { filePath = filePath, fileName = fileName });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تصدير التقرير", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على إحصائيات التقارير
        /// </summary>
        /// <returns>إحصائيات التقارير</returns>
        /// <response code="200">إرجاع إحصائيات التقارير</response>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetReportsStatistics()
        {
            var totalReports = await _context.Reports.CountAsync(r => !r.IsDeleted);
            var publicReports = await _context.Reports.CountAsync(r => r.IsPublic && !r.IsDeleted);
            var sharedReports = await _context.Reports.CountAsync(r => r.IsShared && !r.IsDeleted);

            var reportsByType = await _context.Reports
                .Where(r => !r.IsDeleted)
                .GroupBy(r => r.ReportType)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .ToListAsync();

            var reportsCreatedThisMonth = await _context.Reports
                .Where(r => !r.IsDeleted && r.CreatedAt >= DateTimeOffset.UtcNow.AddMonths(-1).ToUnixTimeSeconds())
                .CountAsync();

            return Ok(new
            {
                TotalReports = totalReports,
                PublicReports = publicReports,
                SharedReports = sharedReports,
                ReportsByType = reportsByType,
                ReportsCreatedThisMonth = reportsCreatedThisMonth
            });
        }

        /// <summary>
        /// نسخ تقرير موجود
        /// </summary>
        /// <param name="id">معرف التقرير المراد نسخه</param>
        /// <param name="request">بيانات النسخ</param>
        /// <returns>التقرير المنسوخ</returns>
        /// <response code="201">تم النسخ بنجاح</response>
        /// <response code="404">التقرير غير موجود</response>
        [HttpPost("{id}/duplicate")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Report>> DuplicateReport(int id, [FromBody] DuplicateReportRequest request)
        {
            var originalReport = await _context.Reports.FindAsync(id);
            if (originalReport == null || originalReport.IsDeleted)
            {
                return NotFound();
            }

            var duplicatedReport = new Report
            {
                Title = request.NewTitle ?? $"{originalReport.Title} (نسخة)",
                Description = originalReport.Description,
                ReportType = originalReport.ReportType,
                Query = originalReport.Query,
                Parameters = originalReport.Parameters,
                CreatedBy = GetCurrentUserId(),
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                IsPublic = false,
                IsShared = false,
                IsDeleted = false
            };

            _context.Reports.Add(duplicatedReport);
            await _context.SaveChangesAsync();

            // تسجيل عملية النسخ
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "duplicate_report",
                    "report",
                    duplicatedReport.Id,
                    currentUserId,
                    $"نسخ تقرير: {originalReport.Title} إلى {duplicatedReport.Title}");
            }

            return CreatedAtAction("GetReport", new { id = duplicatedReport.Id }, duplicatedReport);
        }

        /// <summary>
        /// البحث في التقارير مع تصفية متقدمة
        /// </summary>
        /// <param name="request">معايير البحث</param>
        /// <returns>نتائج البحث مع ترقيم الصفحات</returns>
        /// <response code="200">إرجاع نتائج البحث</response>
        [HttpPost("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> SearchReports([FromBody] SearchReportsRequest request)
        {
            var query = _context.Reports.Where(r => !r.IsDeleted);

            // تطبيق فلاتر البحث
            if (!string.IsNullOrEmpty(request.SearchText))
            {
                query = query.Where(r => r.Title.Contains(request.SearchText) ||
                                        (r.Description != null && r.Description.Contains(request.SearchText)));
            }

            if (!string.IsNullOrEmpty(request.ReportType))
            {
                query = query.Where(r => r.ReportType == request.ReportType);
            }

            if (request.CreatedBy.HasValue)
            {
                query = query.Where(r => r.CreatedBy == request.CreatedBy.Value);
            }

            if (request.CreatedAfter.HasValue)
            {
                var timestamp = ((DateTimeOffset)request.CreatedAfter.Value).ToUnixTimeSeconds();
                query = query.Where(r => r.CreatedAt >= timestamp);
            }

            if (request.CreatedBefore.HasValue)
            {
                var timestamp = ((DateTimeOffset)request.CreatedBefore.Value).ToUnixTimeSeconds();
                query = query.Where(r => r.CreatedAt <= timestamp);
            }

            if (request.PublicOnly == true)
            {
                query = query.Where(r => r.IsPublic);
            }

            if (request.SharedOnly == true)
            {
                query = query.Where(r => r.IsShared);
            }

            // تطبيق الترتيب
            query = request.SortBy?.ToLower() switch
            {
                "title" => request.SortDescending ? query.OrderByDescending(r => r.Title) : query.OrderBy(r => r.Title),
                "reporttype" => request.SortDescending ? query.OrderByDescending(r => r.ReportType) : query.OrderBy(r => r.ReportType),
                "updatedat" => request.SortDescending ? query.OrderByDescending(r => r.UpdatedAt) : query.OrderBy(r => r.UpdatedAt),
                _ => request.SortDescending ? query.OrderByDescending(r => r.CreatedAt) : query.OrderBy(r => r.CreatedAt)
            };

            // حساب العدد الإجمالي
            var totalCount = await query.CountAsync();

            // تطبيق ترقيم الصفحات
            var reports = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            return Ok(new
            {
                Reports = reports,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
            });
        }

        /// <summary>
        /// تحديث إعدادات التقرير
        /// </summary>
        /// <param name="id">معرف التقرير</param>
        /// <param name="request">إعدادات التحديث</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم التحديث بنجاح</response>
        /// <response code="404">التقرير غير موجود</response>
        [HttpPatch("{id}/settings")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateReportSettings(int id, [FromBody] UpdateReportSettingsRequest request)
        {
            var report = await _context.Reports.FindAsync(id);
            if (report == null || report.IsDeleted)
            {
                return NotFound();
            }

            // تحديث الإعدادات
            if (request.IsPublic.HasValue)
                report.IsPublic = request.IsPublic.Value;

            if (request.IsShared.HasValue)
                report.IsShared = request.IsShared.Value;

            if (request.SharedWithUserIds != null)
            {
                // تحديث قائمة المستخدمين المشاركين
                report.SharedWithUserId = string.Join(",", request.SharedWithUserIds);
            }

            report.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            // تسجيل التحديث
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "update_report_settings",
                    "report",
                    id,
                    currentUserId,
                    $"تحديث إعدادات تقرير: {report.Title}");
            }

            return NoContent();
        }

        private bool ReportExists(int id)
        {
            return _context.Reports.Any(e => e.Id == id && !e.IsDeleted);
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي من JWT token
        /// </summary>
        private int GetCurrentUserId()
        {
            try
            {
                var userIdClaim = User.FindFirst("UserId")?.Value ??
                                 User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

                if (int.TryParse(userIdClaim, out int userId))
                {
                    return userId;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }
    }
}
