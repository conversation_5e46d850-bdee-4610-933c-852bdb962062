import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';
import '../../constants/app_colors.dart';
import '../../services/performance_testing_service.dart';
import '../../services/unified_permission_service.dart';

/// شاشة اختبار الأداء
class PerformanceTestingScreen extends StatefulWidget {
  const PerformanceTestingScreen({super.key});

  @override
  State<PerformanceTestingScreen> createState() => _PerformanceTestingScreenState();
}

class _PerformanceTestingScreenState extends State<PerformanceTestingScreen> with TickerProviderStateMixin {
  final PerformanceTestingService _testingService = PerformanceTestingService();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  
  late TabController _tabController;
  
  bool _isRunning = false;
  String _currentProgress = '';
  List<PerformanceTestResult> _results = [];
  Map<String, dynamic>? _performanceReport;
  
  // إعدادات الاختبار
  int _smallDataSize = 100;
  int _mediumDataSize = 1000;
  int _largeDataSize = 10000;
  int _iterations = 3;
  bool _enableMemoryProfiling = true;
  bool _enableNetworkProfiling = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // الاستماع لنتائج الاختبارات
    _testingService.resultStream.listen((result) {
      setState(() {
        _results.add(result);
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _testingService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_permissionService.canAccessSystemSettings()) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('اختبار الأداء'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.lock, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'ليس لديك صلاحية للوصول لاختبارات الأداء',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الأداء'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
            tooltip: 'إعدادات الاختبار',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
            tooltip: 'مساعدة',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'تشغيل الاختبارات', icon: Icon(Icons.play_circle)),
            Tab(text: 'النتائج', icon: Icon(Icons.assessment)),
            Tab(text: 'التقرير', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTestRunTab(),
          _buildResultsTab(),
          _buildReportTab(),
        ],
      ),
    );
  }

  Widget _buildTestRunTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('إعدادات الاختبار الحالية', style: AppStyles.titleMedium),
                  const SizedBox(height: 16),
                  _buildSettingSummary('البيانات الصغيرة', '$_smallDataSize سجل'),
                  _buildSettingSummary('البيانات المتوسطة', '$_mediumDataSize سجل'),
                  _buildSettingSummary('البيانات الكبيرة', '$_largeDataSize سجل'),
                  _buildSettingSummary('عدد التكرارات', '$_iterations مرات'),
                  _buildSettingSummary('تحليل الذاكرة', _enableMemoryProfiling ? 'مفعل' : 'معطل'),
                  _buildSettingSummary('تحليل الشبكة', _enableNetworkProfiling ? 'مفعل' : 'معطل'),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          if (_isRunning) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(
                      'جاري تشغيل الاختبارات...',
                      style: AppStyles.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _currentProgress,
                      style: TextStyle(color: Colors.grey[600]),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'النتائج المكتملة: ${_results.length}',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('اختبارات الأداء المتاحة', style: AppStyles.titleMedium),
                    const SizedBox(height: 16),
                    _buildTestOption(
                      'اختبار شامل',
                      'تشغيل جميع اختبارات الأداء',
                      Icons.speed,
                      Colors.blue,
                      () => _runComprehensiveTests(),
                    ),
                    _buildTestOption(
                      'اختبار تحميل البيانات',
                      'اختبار سرعة تحميل البيانات فقط',
                      Icons.download,
                      Colors.green,
                      () => _runDataLoadingTests(),
                    ),
                    _buildTestOption(
                      'اختبار التصدير',
                      'اختبار أداء تصدير التقارير',
                      Icons.file_download,
                      Colors.orange,
                      () => _runExportTests(),
                    ),
                    _buildTestOption(
                      'اختبار الذاكرة',
                      'تحليل استخدام الذاكرة',
                      Icons.memory,
                      Colors.purple,
                      () => _runMemoryTests(),
                    ),
                  ],
                ),
              ),
            ),
          ],
          
          const Spacer(),
          
          if (!_isRunning)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _runComprehensiveTests,
                icon: const Icon(Icons.play_arrow),
                label: const Text('تشغيل الاختبار الشامل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSettingSummary(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildTestOption(String title, String description, IconData icon, Color color, VoidCallback onTap) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withOpacity(0.1),
        child: Icon(icon, color: color),
      ),
      title: Text(title),
      subtitle: Text(description),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: onTap,
    );
  }

  Widget _buildResultsTab() {
    if (_results.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assessment_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد نتائج اختبار متاحة',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'قم بتشغيل الاختبارات لعرض النتائج',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _results.length,
      itemBuilder: (context, index) {
        final result = _results[index];
        return _buildResultCard(result);
      },
    );
  }

  Widget _buildResultCard(PerformanceTestResult result) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: result.success ? Colors.green : Colors.red,
          child: Icon(
            result.success ? Icons.check : Icons.error,
            color: Colors.white,
          ),
        ),
        title: Text(result.testName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('وقت التنفيذ: ${result.executionTime.inMilliseconds} مللي ثانية'),
            Text('حجم البيانات: ${result.dataSize} سجل'),
            if (result.success)
              Text(
                'الإنتاجية: ${result.throughput.toStringAsFixed(1)} سجل/ثانية',
                style: const TextStyle(color: Colors.blue, fontWeight: FontWeight.w500),
              ),
            if (!result.success && result.errorMessage != null)
              Text(
                'خطأ: ${result.errorMessage}',
                style: const TextStyle(color: Colors.red),
              ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.info_outline),
          onPressed: () => _showResultDetails(result),
        ),
      ),
    );
  }

  Widget _buildReportTab() {
    if (_performanceReport == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.analytics_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا يوجد تقرير أداء متاح',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'قم بتشغيل الاختبارات وإنشاء التقرير',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    final summary = _performanceReport!['summary'] as Map<String, dynamic>;
    final performance = _performanceReport!['performance'] as Map<String, dynamic>;
    final recommendations = _performanceReport!['recommendations'] as List<dynamic>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص الأداء
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('ملخص الأداء', style: AppStyles.titleMedium),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildMetricCard(
                          'إجمالي الاختبارات',
                          summary['totalTests'].toString(),
                          Icons.list,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildMetricCard(
                          'معدل النجاح',
                          '${summary['successRate'].toStringAsFixed(1)}%',
                          Icons.check_circle,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: _buildMetricCard(
                          'متوسط وقت التنفيذ',
                          '${performance['averageExecutionTime'].toStringAsFixed(0)} مللي ثانية',
                          Icons.timer,
                          Colors.orange,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildMetricCard(
                          'متوسط الإنتاجية',
                          '${performance['averageThroughput'].toStringAsFixed(1)} سجل/ثانية',
                          Icons.speed,
                          Colors.purple,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // التوصيات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('التوصيات', style: AppStyles.titleMedium),
                  const SizedBox(height: 16),
                  ...recommendations.map((rec) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(Icons.lightbulb, color: Colors.amber, size: 20),
                        const SizedBox(width: 8),
                        Expanded(child: Text(rec.toString())),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // أزرار الإجراءات
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _exportReport,
                  icon: const Icon(Icons.file_download),
                  label: const Text('تصدير التقرير'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _shareReport,
                  icon: const Icon(Icons.share),
                  label: const Text('مشاركة التقرير'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _runComprehensiveTests() async {
    setState(() {
      _isRunning = true;
      _results.clear();
      _currentProgress = 'بدء الاختبارات...';
    });

    try {
      final config = PerformanceTestConfig(
        smallDataSize: _smallDataSize,
        mediumDataSize: _mediumDataSize,
        largeDataSize: _largeDataSize,
        iterations: _iterations,
        enableMemoryProfiling: _enableMemoryProfiling,
        enableNetworkProfiling: _enableNetworkProfiling,
      );

      await _testingService.runComprehensiveTests(
        config: config,
        onProgress: (progress) {
          setState(() {
            _currentProgress = progress;
          });
        },
      );

      // إنشاء تقرير الأداء
      _performanceReport = _testingService.generatePerformanceReport();

      Get.snackbar(
        'اكتملت الاختبارات',
        'تم إكمال جميع اختبارات الأداء بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

    } catch (e) {
      Get.snackbar(
        'خطأ في الاختبارات',
        'فشل في تشغيل الاختبارات: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isRunning = false;
        _currentProgress = '';
      });
    }
  }

  Future<void> _runDataLoadingTests() async {
    // TODO: تنفيذ اختبار تحميل البيانات فقط
    Get.snackbar('قريباً', 'اختبارات تحميل البيانات قيد التطوير');
  }

  Future<void> _runExportTests() async {
    // TODO: تنفيذ اختبار التصدير فقط
    Get.snackbar('قريباً', 'اختبارات التصدير قيد التطوير');
  }

  Future<void> _runMemoryTests() async {
    // TODO: تنفيذ اختبار الذاكرة فقط
    Get.snackbar('قريباً', 'اختبارات الذاكرة قيد التطوير');
  }

  void _showSettingsDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('إعدادات الاختبار'),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'البيانات الصغيرة'),
                keyboardType: TextInputType.number,
                controller: TextEditingController(text: _smallDataSize.toString()),
                onChanged: (value) => _smallDataSize = int.tryParse(value) ?? _smallDataSize,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'البيانات المتوسطة'),
                keyboardType: TextInputType.number,
                controller: TextEditingController(text: _mediumDataSize.toString()),
                onChanged: (value) => _mediumDataSize = int.tryParse(value) ?? _mediumDataSize,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'البيانات الكبيرة'),
                keyboardType: TextInputType.number,
                controller: TextEditingController(text: _largeDataSize.toString()),
                onChanged: (value) => _largeDataSize = int.tryParse(value) ?? _largeDataSize,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'عدد التكرارات'),
                keyboardType: TextInputType.number,
                controller: TextEditingController(text: _iterations.toString()),
                onChanged: (value) => _iterations = int.tryParse(value) ?? _iterations,
              ),
              CheckboxListTile(
                title: const Text('تحليل الذاكرة'),
                value: _enableMemoryProfiling,
                onChanged: (value) => setState(() => _enableMemoryProfiling = value ?? true),
              ),
              CheckboxListTile(
                title: const Text('تحليل الشبكة'),
                value: _enableNetworkProfiling,
                onChanged: (value) => setState(() => _enableNetworkProfiling = value ?? true),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              setState(() {});
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('مساعدة اختبار الأداء'),
        content: const SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('اختبارات الأداء تساعد في:'),
              SizedBox(height: 8),
              Text('• قياس سرعة تحميل البيانات'),
              Text('• تحليل أداء إنشاء التقارير'),
              Text('• اختبار سرعة التصدير'),
              Text('• مراقبة استخدام الذاكرة'),
              Text('• قياس أداء الشبكة'),
              SizedBox(height: 16),
              Text('يُنصح بتشغيل الاختبارات بانتظام لضمان الأداء الأمثل.'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showResultDetails(PerformanceTestResult result) {
    Get.dialog(
      AlertDialog(
        title: Text(result.testName),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('الحالة: ${result.success ? "نجح" : "فشل"}'),
              Text('وقت التنفيذ: ${result.executionTime.inMilliseconds} مللي ثانية'),
              Text('حجم البيانات: ${result.dataSize} سجل'),
              if (result.success)
                Text('الإنتاجية: ${result.throughput.toStringAsFixed(2)} سجل/ثانية'),
              if (result.errorMessage != null)
                Text('رسالة الخطأ: ${result.errorMessage}'),
              const SizedBox(height: 16),
              const Text('المقاييس الإضافية:'),
              ...result.metrics.entries.map((entry) => 
                Text('${entry.key}: ${entry.value}')
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _exportReport() {
    // TODO: تصدير تقرير الأداء
    Get.snackbar('قريباً', 'تصدير تقرير الأداء قيد التطوير');
  }

  void _shareReport() {
    // TODO: مشاركة تقرير الأداء
    Get.snackbar('قريباً', 'مشاركة تقرير الأداء قيد التطوير');
  }
}
