import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../models/report_models.dart';
import '../controllers/reports_controller.dart';
import 'api/api_service.dart';

/// نتيجة اختبار الأداء
class PerformanceTestResult {
  final String testName;
  final Duration executionTime;
  final int dataSize;
  final bool success;
  final String? errorMessage;
  final Map<String, dynamic> metrics;
  final DateTime timestamp;

  PerformanceTestResult({
    required this.testName,
    required this.executionTime,
    required this.dataSize,
    required this.success,
    this.errorMessage,
    required this.metrics,
    required this.timestamp,
  });

  double get throughput => dataSize / executionTime.inMilliseconds * 1000; // records per second
  
  Map<String, dynamic> toJson() {
    return {
      'testName': testName,
      'executionTime': executionTime.inMilliseconds,
      'dataSize': dataSize,
      'success': success,
      'errorMessage': errorMessage,
      'metrics': metrics,
      'timestamp': timestamp.toIso8601String(),
      'throughput': throughput,
    };
  }
}

/// إعدادات اختبار الأداء
class PerformanceTestConfig {
  final int smallDataSize;
  final int mediumDataSize;
  final int largeDataSize;
  final int iterations;
  final Duration timeout;
  final bool enableMemoryProfiling;
  final bool enableNetworkProfiling;

  const PerformanceTestConfig({
    this.smallDataSize = 100,
    this.mediumDataSize = 1000,
    this.largeDataSize = 10000,
    this.iterations = 3,
    this.timeout = const Duration(minutes: 5),
    this.enableMemoryProfiling = true,
    this.enableNetworkProfiling = true,
  });
}

/// خدمة اختبار الأداء للتقارير
class PerformanceTestingService {
  final ApiService _apiService = ApiService();
  final ReportsController _reportsController = Get.find<ReportsController>();
  
  final List<PerformanceTestResult> _testResults = [];
  final StreamController<PerformanceTestResult> _resultStream = StreamController.broadcast();
  
  Stream<PerformanceTestResult> get resultStream => _resultStream.stream;
  List<PerformanceTestResult> get testResults => List.unmodifiable(_testResults);

  /// تشغيل مجموعة اختبارات الأداء الشاملة
  Future<List<PerformanceTestResult>> runComprehensiveTests({
    PerformanceTestConfig? config,
    Function(String)? onProgress,
  }) async {
    final testConfig = config ?? const PerformanceTestConfig();
    _testResults.clear();
    
    onProgress?.call('بدء اختبارات الأداء الشاملة...');
    
    // اختبارات تحميل البيانات
    await _runDataLoadingTests(testConfig, onProgress);
    
    // اختبارات إنشاء التقارير
    await _runReportGenerationTests(testConfig, onProgress);
    
    // اختبارات التصدير
    await _runExportTests(testConfig, onProgress);
    
    // اختبارات التصورات المرئية
    await _runVisualizationTests(testConfig, onProgress);
    
    // اختبارات الذاكرة
    if (testConfig.enableMemoryProfiling) {
      await _runMemoryTests(testConfig, onProgress);
    }
    
    // اختبارات الشبكة
    if (testConfig.enableNetworkProfiling) {
      await _runNetworkTests(testConfig, onProgress);
    }
    
    onProgress?.call('تم إكمال جميع اختبارات الأداء');
    
    return _testResults;
  }

  /// اختبارات تحميل البيانات
  Future<void> _runDataLoadingTests(PerformanceTestConfig config, Function(String)? onProgress) async {
    onProgress?.call('اختبار تحميل البيانات...');
    
    // اختبار تحميل بيانات صغيرة
    await _runSingleTest(
      'تحميل بيانات صغيرة',
      config.smallDataSize,
      () => _loadTestData(config.smallDataSize),
      config,
    );
    
    // اختبار تحميل بيانات متوسطة
    await _runSingleTest(
      'تحميل بيانات متوسطة',
      config.mediumDataSize,
      () => _loadTestData(config.mediumDataSize),
      config,
    );
    
    // اختبار تحميل بيانات كبيرة
    await _runSingleTest(
      'تحميل بيانات كبيرة',
      config.largeDataSize,
      () => _loadTestData(config.largeDataSize),
      config,
    );
  }

  /// اختبارات إنشاء التقارير
  Future<void> _runReportGenerationTests(PerformanceTestConfig config, Function(String)? onProgress) async {
    onProgress?.call('اختبار إنشاء التقارير...');
    
    for (final reportType in ReportType.values) {
      await _runSingleTest(
        'إنشاء تقرير ${reportType.displayName}',
        config.mediumDataSize,
        () => _generateTestReport(reportType, config.mediumDataSize),
        config,
      );
    }
  }

  /// اختبارات التصدير
  Future<void> _runExportTests(PerformanceTestConfig config, Function(String)? onProgress) async {
    onProgress?.call('اختبار التصدير...');
    
    // اختبار تصدير PDF
    await _runSingleTest(
      'تصدير PDF',
      config.mediumDataSize,
      () => _testExport('pdf', config.mediumDataSize),
      config,
    );
    
    // اختبار تصدير Excel
    await _runSingleTest(
      'تصدير Excel',
      config.mediumDataSize,
      () => _testExport('excel', config.mediumDataSize),
      config,
    );
    
    // اختبار تصدير CSV
    await _runSingleTest(
      'تصدير CSV',
      config.largeDataSize,
      () => _testExport('csv', config.largeDataSize),
      config,
    );
  }

  /// اختبارات التصورات المرئية
  Future<void> _runVisualizationTests(PerformanceTestConfig config, Function(String)? onProgress) async {
    onProgress?.call('اختبار التصورات المرئية...');
    
    // اختبار رسم المخططات
    await _runSingleTest(
      'رسم مخطط أعمدة',
      config.mediumDataSize,
      () => _testVisualization('bar_chart', config.mediumDataSize),
      config,
    );
    
    await _runSingleTest(
      'رسم مخطط دائري',
      config.smallDataSize,
      () => _testVisualization('pie_chart', config.smallDataSize),
      config,
    );
    
    await _runSingleTest(
      'رسم مخطط خطي',
      config.largeDataSize,
      () => _testVisualization('line_chart', config.largeDataSize),
      config,
    );
  }

  /// اختبارات الذاكرة
  Future<void> _runMemoryTests(PerformanceTestConfig config, Function(String)? onProgress) async {
    onProgress?.call('اختبار استخدام الذاكرة...');
    
    await _runSingleTest(
      'استخدام الذاكرة - بيانات كبيرة',
      config.largeDataSize,
      () => _testMemoryUsage(config.largeDataSize),
      config,
    );
  }

  /// اختبارات الشبكة
  Future<void> _runNetworkTests(PerformanceTestConfig config, Function(String)? onProgress) async {
    onProgress?.call('اختبار أداء الشبكة...');
    
    await _runSingleTest(
      'سرعة الشبكة - تحميل',
      config.mediumDataSize,
      () => _testNetworkDownload(config.mediumDataSize),
      config,
    );
    
    await _runSingleTest(
      'سرعة الشبكة - رفع',
      config.smallDataSize,
      () => _testNetworkUpload(config.smallDataSize),
      config,
    );
  }

  /// تشغيل اختبار واحد
  Future<void> _runSingleTest(
    String testName,
    int dataSize,
    Future<Map<String, dynamic>> Function() testFunction,
    PerformanceTestConfig config,
  ) async {
    final results = <PerformanceTestResult>[];
    
    for (int i = 0; i < config.iterations; i++) {
      try {
        final stopwatch = Stopwatch()..start();
        
        final metrics = await testFunction().timeout(config.timeout);
        
        stopwatch.stop();
        
        final result = PerformanceTestResult(
          testName: '$testName (${i + 1}/${config.iterations})',
          executionTime: stopwatch.elapsed,
          dataSize: dataSize,
          success: true,
          metrics: metrics,
          timestamp: DateTime.now(),
        );
        
        results.add(result);
        _testResults.add(result);
        _resultStream.add(result);
        
      } catch (e) {
        final result = PerformanceTestResult(
          testName: '$testName (${i + 1}/${config.iterations})',
          executionTime: Duration.zero,
          dataSize: dataSize,
          success: false,
          errorMessage: e.toString(),
          metrics: {},
          timestamp: DateTime.now(),
        );
        
        results.add(result);
        _testResults.add(result);
        _resultStream.add(result);
      }
      
      // فترة راحة بين الاختبارات
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  /// تحميل بيانات اختبارية
  Future<Map<String, dynamic>> _loadTestData(int size) async {
    final data = List.generate(size, (index) => {
      'id': index,
      'name': 'عنصر $index',
      'value': Random().nextInt(1000),
      'category': 'فئة ${index % 10}',
      'date': DateTime.now().subtract(Duration(days: index % 365)).toIso8601String(),
    });
    
    // محاكاة تحميل من API
    await Future.delayed(Duration(milliseconds: size ~/ 10));
    
    return {
      'data': data,
      'memoryUsage': _estimateMemoryUsage(data),
      'processingTime': size ~/ 10,
    };
  }

  /// إنشاء تقرير اختباري
  Future<Map<String, dynamic>> _generateTestReport(ReportType reportType, int dataSize) async {
    final data = await _loadTestData(dataSize);
    
    // محاكاة معالجة التقرير
    await Future.delayed(Duration(milliseconds: dataSize ~/ 5));
    
    return {
      'reportType': reportType.value,
      'dataSize': dataSize,
      'processingComplexity': _calculateComplexity(reportType),
      'memoryUsage': data['memoryUsage'],
    };
  }

  /// اختبار التصدير
  Future<Map<String, dynamic>> _testExport(String format, int dataSize) async {
    final data = await _loadTestData(dataSize);
    
    // محاكاة عملية التصدير
    final exportTime = _calculateExportTime(format, dataSize);
    await Future.delayed(Duration(milliseconds: exportTime));
    
    return {
      'format': format,
      'dataSize': dataSize,
      'exportTime': exportTime,
      'fileSize': _estimateFileSize(format, dataSize),
    };
  }

  /// اختبار التصور المرئي
  Future<Map<String, dynamic>> _testVisualization(String chartType, int dataSize) async {
    final data = await _loadTestData(dataSize);
    
    // محاكاة رسم المخطط
    final renderTime = _calculateRenderTime(chartType, dataSize);
    await Future.delayed(Duration(milliseconds: renderTime));
    
    return {
      'chartType': chartType,
      'dataPoints': dataSize,
      'renderTime': renderTime,
      'complexity': _calculateVisualizationComplexity(chartType),
    };
  }

  /// اختبار استخدام الذاكرة
  Future<Map<String, dynamic>> _testMemoryUsage(int dataSize) async {
    final data = await _loadTestData(dataSize);
    
    // محاكاة عمليات معالجة مكثفة للذاكرة
    final processedData = data['data'] as List;
    final duplicatedData = List.from(processedData)..addAll(processedData);
    
    return {
      'originalSize': dataSize,
      'processedSize': duplicatedData.length,
      'memoryUsage': _estimateMemoryUsage(duplicatedData),
      'gcPressure': duplicatedData.length > 5000,
    };
  }

  /// اختبار تحميل الشبكة
  Future<Map<String, dynamic>> _testNetworkDownload(int dataSize) async {
    final startTime = DateTime.now();
    
    try {
      // محاكاة تحميل من API
      await _apiService.get('/api/Reports/test-data?size=$dataSize');
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      return {
        'dataSize': dataSize,
        'downloadTime': duration.inMilliseconds,
        'bandwidth': dataSize / duration.inMilliseconds * 1000, // bytes per second
        'success': true,
      };
    } catch (e) {
      return {
        'dataSize': dataSize,
        'downloadTime': 0,
        'bandwidth': 0,
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// اختبار رفع الشبكة
  Future<Map<String, dynamic>> _testNetworkUpload(int dataSize) async {
    final testData = List.generate(dataSize, (index) => 'test_data_$index');
    final startTime = DateTime.now();
    
    try {
      // محاكاة رفع إلى API
      await _apiService.post('/api/Reports/test-upload', {'data': testData});
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      return {
        'dataSize': dataSize,
        'uploadTime': duration.inMilliseconds,
        'bandwidth': dataSize / duration.inMilliseconds * 1000,
        'success': true,
      };
    } catch (e) {
      return {
        'dataSize': dataSize,
        'uploadTime': 0,
        'bandwidth': 0,
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // دوال مساعدة لحساب المقاييس

  int _estimateMemoryUsage(List data) {
    // تقدير تقريبي لاستخدام الذاكرة بالبايت
    return data.length * 200; // متوسط 200 بايت لكل عنصر
  }

  int _calculateComplexity(ReportType reportType) {
    switch (reportType) {
      case ReportType.taskSummary:
        return 3;
      case ReportType.userPerformance:
        return 5;
      case ReportType.departmentPerformance:
        return 7;
      default:
        return 4;
    }
  }

  int _calculateExportTime(String format, int dataSize) {
    final baseTime = dataSize ~/ 20;
    switch (format) {
      case 'pdf':
        return baseTime * 3;
      case 'excel':
        return baseTime * 2;
      case 'csv':
        return baseTime;
      default:
        return baseTime * 2;
    }
  }

  int _estimateFileSize(String format, int dataSize) {
    final baseSize = dataSize * 50; // 50 bytes per record
    switch (format) {
      case 'pdf':
        return baseSize * 3;
      case 'excel':
        return baseSize * 2;
      case 'csv':
        return baseSize;
      default:
        return baseSize;
    }
  }

  int _calculateRenderTime(String chartType, int dataSize) {
    final baseTime = dataSize ~/ 50;
    switch (chartType) {
      case 'pie_chart':
        return baseTime;
      case 'bar_chart':
        return baseTime * 2;
      case 'line_chart':
        return baseTime * 3;
      default:
        return baseTime * 2;
    }
  }

  int _calculateVisualizationComplexity(String chartType) {
    switch (chartType) {
      case 'pie_chart':
        return 2;
      case 'bar_chart':
        return 3;
      case 'line_chart':
        return 4;
      default:
        return 3;
    }
  }

  /// إنشاء تقرير أداء شامل
  Map<String, dynamic> generatePerformanceReport() {
    if (_testResults.isEmpty) {
      return {'error': 'لا توجد نتائج اختبار متاحة'};
    }

    final successfulTests = _testResults.where((r) => r.success).toList();
    final failedTests = _testResults.where((r) => !r.success).toList();

    return {
      'summary': {
        'totalTests': _testResults.length,
        'successfulTests': successfulTests.length,
        'failedTests': failedTests.length,
        'successRate': successfulTests.length / _testResults.length * 100,
      },
      'performance': {
        'averageExecutionTime': successfulTests.isEmpty ? 0 : 
          successfulTests.map((r) => r.executionTime.inMilliseconds).reduce((a, b) => a + b) / successfulTests.length,
        'averageThroughput': successfulTests.isEmpty ? 0 :
          successfulTests.map((r) => r.throughput).reduce((a, b) => a + b) / successfulTests.length,
        'fastestTest': successfulTests.isEmpty ? null :
          successfulTests.reduce((a, b) => a.executionTime < b.executionTime ? a : b).testName,
        'slowestTest': successfulTests.isEmpty ? null :
          successfulTests.reduce((a, b) => a.executionTime > b.executionTime ? a : b).testName,
      },
      'recommendations': _generateRecommendations(),
      'testResults': _testResults.map((r) => r.toJson()).toList(),
    };
  }

  List<String> _generateRecommendations() {
    final recommendations = <String>[];
    
    final successfulTests = _testResults.where((r) => r.success).toList();
    if (successfulTests.isEmpty) return ['لا توجد اختبارات ناجحة لتحليلها'];

    final avgTime = successfulTests.map((r) => r.executionTime.inMilliseconds).reduce((a, b) => a + b) / successfulTests.length;
    
    if (avgTime > 5000) {
      recommendations.add('الأداء بطيء - يُنصح بتحسين خوارزميات المعالجة');
    }
    
    final failedTests = _testResults.where((r) => !r.success).toList();
    if (failedTests.length > _testResults.length * 0.1) {
      recommendations.add('معدل فشل عالي - يُنصح بمراجعة استقرار النظام');
    }
    
    final largeDataTests = successfulTests.where((r) => r.dataSize > 5000).toList();
    if (largeDataTests.any((r) => r.executionTime.inSeconds > 30)) {
      recommendations.add('أداء ضعيف مع البيانات الكبيرة - يُنصح بتطبيق التحميل التدريجي');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('الأداء جيد - لا توجد توصيات خاصة');
    }
    
    return recommendations;
  }

  void dispose() {
    _resultStream.close();
  }
}
