using System.ComponentModel.DataAnnotations;

namespace webApi.Models
{
    /// <summary>
    /// طلب تصدير التقرير
    /// </summary>
    public class ExportReportRequest
    {
        /// <summary>
        /// تنسيق التصدير (pdf, excel, csv, json)
        /// </summary>
        [Required]
        public string Format { get; set; } = null!;

        /// <summary>
        /// معايير إضافية للتصدير
        /// </summary>
        public Dictionary<string, object>? Parameters { get; set; }

        /// <summary>
        /// تضمين البيانات الوصفية
        /// </summary>
        public bool IncludeMetadata { get; set; } = true;

        /// <summary>
        /// العلامة المائية
        /// </summary>
        public string? Watermark { get; set; }
    }

    /// <summary>
    /// طلب نسخ التقرير
    /// </summary>
    public class DuplicateReportRequest
    {
        /// <summary>
        /// العنوان الجديد للتقرير المنسوخ
        /// </summary>
        public string? NewTitle { get; set; }

        /// <summary>
        /// وصف جديد (اختياري)
        /// </summary>
        public string? NewDescription { get; set; }

        /// <summary>
        /// نسخ الجدولة أيضاً
        /// </summary>
        public bool CopySchedules { get; set; } = false;
    }

    /// <summary>
    /// طلب إنشاء تقرير مخصص
    /// </summary>
    public class CreateCustomReportRequest
    {
        /// <summary>
        /// عنوان التقرير
        /// </summary>
        [Required]
        [StringLength(255)]
        public string Title { get; set; } = null!;

        /// <summary>
        /// وصف التقرير
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// نوع التقرير
        /// </summary>
        [Required]
        public string ReportType { get; set; } = null!;

        /// <summary>
        /// معايير التقرير
        /// </summary>
        public ReportCriteria? Criteria { get; set; }

        /// <summary>
        /// إعدادات التصور المرئي
        /// </summary>
        public List<VisualizationSettings>? Visualizations { get; set; }

        /// <summary>
        /// هل التقرير عام
        /// </summary>
        public bool IsPublic { get; set; } = false;

        /// <summary>
        /// هل التقرير مشارك
        /// </summary>
        public bool IsShared { get; set; } = false;

        /// <summary>
        /// المستخدمون المشاركون
        /// </summary>
        public List<int>? SharedWithUserIds { get; set; }
    }

    /// <summary>
    /// معايير التقرير
    /// </summary>
    public class ReportCriteria
    {
        /// <summary>
        /// تاريخ البداية
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// تاريخ النهاية
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// معرفات المستخدمين
        /// </summary>
        public List<int>? UserIds { get; set; }

        /// <summary>
        /// معرفات الأقسام
        /// </summary>
        public List<int>? DepartmentIds { get; set; }

        /// <summary>
        /// حالات المهام
        /// </summary>
        public List<string>? TaskStatuses { get; set; }

        /// <summary>
        /// أولويات المهام
        /// </summary>
        public List<string>? TaskPriorities { get; set; }

        /// <summary>
        /// أنواع المهام
        /// </summary>
        public List<int>? TaskTypeIds { get; set; }
    }

    /// <summary>
    /// إعدادات التصور المرئي
    /// </summary>
    public class VisualizationSettings
    {
        /// <summary>
        /// نوع التصور (pie, bar, line, table)
        /// </summary>
        [Required]
        public string Type { get; set; } = null!;

        /// <summary>
        /// عنوان التصور
        /// </summary>
        [Required]
        public string Title { get; set; } = null!;

        /// <summary>
        /// حقل البيانات الرئيسي
        /// </summary>
        public string? DataField { get; set; }

        /// <summary>
        /// حقل التجميع
        /// </summary>
        public string? GroupByField { get; set; }

        /// <summary>
        /// إعدادات إضافية
        /// </summary>
        public Dictionary<string, object>? Settings { get; set; }
    }

    /// <summary>
    /// طلب تحديث إعدادات التقرير
    /// </summary>
    public class UpdateReportSettingsRequest
    {
        /// <summary>
        /// هل التقرير عام
        /// </summary>
        public bool? IsPublic { get; set; }

        /// <summary>
        /// هل التقرير مشارك
        /// </summary>
        public bool? IsShared { get; set; }

        /// <summary>
        /// المستخدمون المشاركون
        /// </summary>
        public List<int>? SharedWithUserIds { get; set; }

        /// <summary>
        /// إعدادات إضافية
        /// </summary>
        public Dictionary<string, object>? Settings { get; set; }
    }

    /// <summary>
    /// طلب البحث في التقارير
    /// </summary>
    public class SearchReportsRequest
    {
        /// <summary>
        /// نص البحث
        /// </summary>
        public string? SearchText { get; set; }

        /// <summary>
        /// نوع التقرير
        /// </summary>
        public string? ReportType { get; set; }

        /// <summary>
        /// معرف المنشئ
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// تاريخ البداية للبحث
        /// </summary>
        public DateTime? CreatedAfter { get; set; }

        /// <summary>
        /// تاريخ النهاية للبحث
        /// </summary>
        public DateTime? CreatedBefore { get; set; }

        /// <summary>
        /// التقارير العامة فقط
        /// </summary>
        public bool? PublicOnly { get; set; }

        /// <summary>
        /// التقارير المشاركة فقط
        /// </summary>
        public bool? SharedOnly { get; set; }

        /// <summary>
        /// حجم الصفحة
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// رقم الصفحة
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// ترتيب النتائج
        /// </summary>
        public string? SortBy { get; set; } = "CreatedAt";

        /// <summary>
        /// اتجاه الترتيب
        /// </summary>
        public bool SortDescending { get; set; } = true;
    }
}
