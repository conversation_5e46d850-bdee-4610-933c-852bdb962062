import 'package:flutter/material.dart';

import '../../../../models/reporting/report_result_model.dart';

/// امتداد لأنواع التصورات المرئية
extension VisualizationTypeExtension on VisualizationType {
  /// الاسم المعروض
  String get displayName {
    switch (this) {
      case VisualizationType.barChart:
        return 'مخطط أعمدة';
      case VisualizationType.lineChart:
        return 'مخطط خطي';
      case VisualizationType.pieChart:
        return 'مخطط دائري';
      case VisualizationType.areaChart:
        return 'مخطط منطقة';
      case VisualizationType.table:
        return 'جدول';
      case VisualizationType.kpiCard:
        return 'بطاقة مؤشر أداء';
      case VisualizationType.gaugeChart:
        return 'مخطط مقياس';
      case VisualizationType.heatMap:
        return 'خريطة حرارية';
      case VisualizationType.radarChart:
        return 'مخطط رادار';
      case VisualizationType.bubbleChart:
        return 'مخطط فقاعي';
      case VisualizationType.ganttChart:
        return 'مخطط جانت';
      case VisualizationType.treeMap:
        return 'خريطة شجرية';
      case VisualizationType.summary:
        return 'ملخص';
      case VisualizationType.custom:
        return 'مخصص';
    }
  }

  /// الأيقونة المناسبة
  IconData get icon {
    switch (this) {
      case VisualizationType.barChart:
        return Icons.bar_chart;
      case VisualizationType.lineChart:
        return Icons.show_chart;
      case VisualizationType.pieChart:
        return Icons.pie_chart;
      case VisualizationType.areaChart:
        return Icons.area_chart;
      case VisualizationType.table:
        return Icons.table_chart;
      case VisualizationType.kpiCard:
        return Icons.dashboard;
      case VisualizationType.gaugeChart:
        return Icons.speed;
      case VisualizationType.heatMap:
        return Icons.grid_on;
      case VisualizationType.radarChart:
        return Icons.radar;
      case VisualizationType.bubbleChart:
        return Icons.bubble_chart;
      case VisualizationType.ganttChart:
        return Icons.timeline;
      case VisualizationType.treeMap:
        return Icons.account_tree;
      case VisualizationType.summary:
        return Icons.summarize;
      case VisualizationType.custom:
        return Icons.extension;
    }
  }

  /// اللون المناسب
  Color get color {
    switch (this) {
      case VisualizationType.barChart:
        return Colors.blue;
      case VisualizationType.lineChart:
        return Colors.green;
      case VisualizationType.pieChart:
        return Colors.orange;
      case VisualizationType.areaChart:
        return Colors.purple;
      case VisualizationType.table:
        return Colors.grey;
      case VisualizationType.kpiCard:
        return Colors.indigo;
      case VisualizationType.gaugeChart:
        return Colors.teal;
      case VisualizationType.heatMap:
        return Colors.red;
      case VisualizationType.radarChart:
        return Colors.cyan;
      case VisualizationType.bubbleChart:
        return Colors.pink;
      case VisualizationType.ganttChart:
        return Colors.brown;
      case VisualizationType.treeMap:
        return Colors.lime;
      case VisualizationType.summary:
        return Colors.amber;
      case VisualizationType.custom:
        return Colors.deepPurple;
    }
  }
}

/// مصنع التصورات المرئية المحسن
class EnhancedVisualizationFactory {
  /// إنشاء تصور مرئي محسن
  static Widget createVisualization({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
    bool enableAnimation = true,
    Function(Map<String, dynamic>)? onDataPointTap,
    Function(String)? onLegendTap,
  }) {
    // التحقق من صحة البيانات
    if (data.isEmpty) {
      return _buildEmptyDataWidget(visualization.title);
    }

    // تطبيق الإعدادات المتقدمة
    final enhancedSettings = _enhanceVisualizationSettings(visualization);

    switch (visualization.type) {
      case VisualizationType.barChart:
        return _createEnhancedBarChart(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
          enableAnimation: enableAnimation,
          onDataPointTap: onDataPointTap,
        );

      case VisualizationType.lineChart:
        return _createEnhancedLineChart(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
          enableAnimation: enableAnimation,
          onDataPointTap: onDataPointTap,
        );

      case VisualizationType.pieChart:
        return _createEnhancedPieChart(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
          enableAnimation: enableAnimation,
          onDataPointTap: onDataPointTap,
        );

      case VisualizationType.areaChart:
        return _createEnhancedAreaChart(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
          enableAnimation: enableAnimation,
        );

      case VisualizationType.table:
        return _createEnhancedTable(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
        );

      case VisualizationType.kpiCard:
        return _createEnhancedKpiCard(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
        );

      case VisualizationType.gaugeChart:
        return _createEnhancedGaugeChart(
          visualization: enhancedSettings,
          data: data,
          enableAnimation: enableAnimation,
        );

      case VisualizationType.heatMap:
        return _createEnhancedHeatMap(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
        );

      case VisualizationType.radarChart:
        return _createEnhancedRadarChart(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
          enableAnimation: enableAnimation,
        );

      case VisualizationType.bubbleChart:
        return _createEnhancedBubbleChart(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
          enableAnimation: enableAnimation,
        );

      case VisualizationType.ganttChart:
        return _createEnhancedGanttChart(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
        );

      case VisualizationType.treeMap:
        return _createEnhancedTreeMap(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
          enableAnimation: enableAnimation,
        );

      case VisualizationType.summary:
        return _createEnhancedSummary(
          visualization: enhancedSettings,
          data: data,
        );

      case VisualizationType.custom:
        return _createCustomVisualization(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
          enableAnimation: enableAnimation,
        );

      default:
        return _createEnhancedTable(
          visualization: enhancedSettings,
          data: data,
          enableInteraction: enableInteraction,
        );
    }
  }

  /// تحسين إعدادات التصور المرئي
  static ReportVisualization _enhanceVisualizationSettings(ReportVisualization visualization) {
    final enhancedSettings = Map<String, dynamic>.from(visualization.settings ?? {});
    
    // إعدادات الألوان المحسنة
    if (!enhancedSettings.containsKey('colorScheme')) {
      enhancedSettings['colorScheme'] = _getDefaultColorScheme(visualization.type);
    }
    
    // إعدادات الخطوط المحسنة
    if (!enhancedSettings.containsKey('fontSettings')) {
      enhancedSettings['fontSettings'] = _getDefaultFontSettings();
    }
    
    // إعدادات التفاعل
    if (!enhancedSettings.containsKey('interactionSettings')) {
      enhancedSettings['interactionSettings'] = _getDefaultInteractionSettings();
    }
    
    // إعدادات الرسوم المتحركة
    if (!enhancedSettings.containsKey('animationSettings')) {
      enhancedSettings['animationSettings'] = _getDefaultAnimationSettings();
    }

    // إنشاء نسخة محسنة من التصور المرئي
    return ReportVisualization(
      id: visualization.id,
      title: visualization.title,
      description: visualization.description,
      type: visualization.type,
      xAxisField: visualization.xAxisField,
      xAxisLabel: visualization.xAxisLabel,
      yAxisField: visualization.yAxisField,
      yAxisLabel: visualization.yAxisLabel,
      dataFields: visualization.dataFields,
      dataLabels: visualization.dataLabels,
      orientation: visualization.orientation,
      showValues: visualization.showValues,
      showLabels: visualization.showLabels,
      showGrid: visualization.showGrid,
      showLegend: visualization.showLegend,
      legendPosition: visualization.legendPosition,
      seriesColors: visualization.seriesColors,
      width: visualization.width,
      height: visualization.height,
      settings: enhancedSettings,
    );
  }

  /// عرض widget للبيانات الفارغة
  static Widget _buildEmptyDataWidget(String title) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.data_usage_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات لعرضها',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'في $title',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// إنشاء مخطط أعمدة محسن
  static Widget _createEnhancedBarChart({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
    bool enableAnimation = true,
    Function(Map<String, dynamic>)? onDataPointTap,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (visualization.title.isNotEmpty) ...[
            Text(
              visualization.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
          ],
          if (visualization.description?.isNotEmpty == true) ...[
            Text(
              visualization.description!,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
          ],
          Expanded(
            child: _buildSimpleBarChart(data, visualization),
          ),
        ],
      ),
    );
  }

  /// إنشاء مخطط خطي محسن
  static Widget _createEnhancedLineChart({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
    bool enableAnimation = true,
    Function(Map<String, dynamic>)? onDataPointTap,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (visualization.title.isNotEmpty) ...[
            Text(
              visualization.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
          ],
          if (visualization.description?.isNotEmpty == true) ...[
            Text(
              visualization.description!,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
          ],
          Expanded(
            child: _buildSimpleLineChart(data, visualization),
          ),
        ],
      ),
    );
  }

  /// إنشاء مخطط دائري محسن
  static Widget _createEnhancedPieChart({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
    bool enableAnimation = true,
    Function(Map<String, dynamic>)? onDataPointTap,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (visualization.title.isNotEmpty) ...[
            Text(
              visualization.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
          ],
          if (visualization.description?.isNotEmpty == true) ...[
            Text(
              visualization.description!,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
          ],
          Expanded(
            child: _buildSimplePieChart(data, visualization),
          ),
        ],
      ),
    );
  }

  /// إنشاء جدول محسن
  static Widget _createEnhancedTable({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (visualization.title.isNotEmpty) ...[
            Text(
              visualization.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
          ],
          Expanded(
            child: _buildSimpleTable(data, visualization),
          ),
        ],
      ),
    );
  }

  /// إنشاء بطاقة مؤشر أداء محسنة
  static Widget _createEnhancedKpiCard({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
  }) {
    return _buildSimpleKpiCard(data, visualization);
  }

  // دوال مساعدة لإنشاء التصورات البسيطة

  /// بناء مخطط أعمدة بسيط
  static Widget _buildSimpleBarChart(List<Map<String, dynamic>> data, ReportVisualization visualization) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Text('مخطط الأعمدة - قيد التطوير'),
      ),
    );
  }

  /// بناء مخطط خطي بسيط
  static Widget _buildSimpleLineChart(List<Map<String, dynamic>> data, ReportVisualization visualization) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Text('المخطط الخطي - قيد التطوير'),
      ),
    );
  }

  /// بناء مخطط دائري بسيط
  static Widget _buildSimplePieChart(List<Map<String, dynamic>> data, ReportVisualization visualization) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Text('المخطط الدائري - قيد التطوير'),
      ),
    );
  }

  /// بناء جدول بسيط
  static Widget _buildSimpleTable(List<Map<String, dynamic>> data, ReportVisualization visualization) {
    if (data.isEmpty) return const Center(child: Text('لا توجد بيانات'));

    final columns = data.first.keys.toList();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: columns.map((column) => DataColumn(
          label: Text(
            column,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        )).toList(),
        rows: data.map((row) => DataRow(
          cells: columns.map((column) => DataCell(
            Text(row[column]?.toString() ?? ''),
          )).toList(),
        )).toList(),
      ),
    );
  }

  /// بناء بطاقة مؤشر أداء بسيطة
  static Widget _buildSimpleKpiCard(List<Map<String, dynamic>> data, ReportVisualization visualization) {
    if (data.isEmpty) return const Center(child: Text('لا توجد بيانات'));

    final firstItem = data.first;
    final value = firstItem.values.first?.toString() ?? '0';

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              visualization.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دوال مساعدة للإعدادات الافتراضية

  /// الحصول على نظام الألوان الافتراضي
  static List<Color> _getDefaultColorScheme(VisualizationType type) {
    switch (type) {
      case VisualizationType.barChart:
        return [Colors.blue, Colors.lightBlue, Colors.cyan];
      case VisualizationType.lineChart:
        return [Colors.green, Colors.lightGreen, Colors.lime];
      case VisualizationType.pieChart:
        return [Colors.orange, Colors.deepOrange, Colors.amber];
      default:
        return [Colors.blue, Colors.green, Colors.orange, Colors.purple, Colors.red];
    }
  }

  /// الحصول على إعدادات الخطوط الافتراضية
  static Map<String, dynamic> _getDefaultFontSettings() {
    return {
      'titleFontSize': 18.0,
      'labelFontSize': 14.0,
      'valueFontSize': 12.0,
      'fontFamily': 'Roboto',
    };
  }

  /// الحصول على إعدادات التفاعل الافتراضية
  static Map<String, dynamic> _getDefaultInteractionSettings() {
    return {
      'enableTooltip': true,
      'enableZoom': true,
      'enablePan': true,
      'enableSelection': true,
    };
  }

  /// الحصول على إعدادات الرسوم المتحركة الافتراضية
  static Map<String, dynamic> _getDefaultAnimationSettings() {
    return {
      'enableAnimation': true,
      'animationDuration': 1000,
      'animationType': 'easeInOut',
    };
  }

  // دوال إضافية للتصورات المتقدمة (ستتم إضافتها لاحقاً)

  static Widget _createEnhancedAreaChart({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
    bool enableAnimation = true,
  }) {
    return const Center(child: Text('مخطط المنطقة - قيد التطوير'));
  }

  static Widget _createEnhancedGaugeChart({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableAnimation = true,
  }) {
    return const Center(child: Text('مخطط المقياس - قيد التطوير'));
  }

  static Widget _createEnhancedHeatMap({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
  }) {
    return const Center(child: Text('الخريطة الحرارية - قيد التطوير'));
  }

  static Widget _createEnhancedRadarChart({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
    bool enableAnimation = true,
  }) {
    return const Center(child: Text('مخطط الرادار - قيد التطوير'));
  }

  static Widget _createEnhancedBubbleChart({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
    bool enableAnimation = true,
  }) {
    return const Center(child: Text('المخطط الفقاعي - قيد التطوير'));
  }

  static Widget _createEnhancedGanttChart({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
  }) {
    return const Center(child: Text('مخطط جانت - قيد التطوير'));
  }

  static Widget _createEnhancedTreeMap({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
    bool enableAnimation = true,
  }) {
    return const Center(child: Text('الخريطة الشجرية - قيد التطوير'));
  }

  static Widget _createEnhancedSummary({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
  }) {
    return const Center(child: Text('الملخص - قيد التطوير'));
  }

  static Widget _createCustomVisualization({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
    bool enableInteraction = true,
    bool enableAnimation = true,
  }) {
    return const Center(child: Text('التصور المخصص - قيد التطوير'));
  }
}
