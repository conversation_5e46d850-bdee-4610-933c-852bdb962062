import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_colors.dart';
import '../../models/report_schedule_models.dart';
import '../../controllers/report_schedules_controller.dart';
import '../../services/unified_permission_service.dart';

/// شاشة إدارة الجدولة المتقدمة للتقارير
class AdvancedSchedulerScreen extends StatefulWidget {
  const AdvancedSchedulerScreen({super.key});

  @override
  State<AdvancedSchedulerScreen> createState() => _AdvancedSchedulerScreenState();
}

class _AdvancedSchedulerScreenState extends State<AdvancedSchedulerScreen> with TickerProviderStateMixin {
  final ReportSchedulesController _schedulesController = Get.find<ReportSchedulesController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  late TabController _tabController;

  List<ReportSchedule> _schedules = [];
  List<ReportSchedule> _activeSchedules = [];
  List<ReportSchedule> _inactiveSchedules = [];
  List<ReportSchedule> _upcomingSchedules = [];

  bool _isLoading = true;
  String? _errorMessage;

  // إحصائيات
  Map<String, dynamic> _statistics = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadSchedules();
    _loadStatistics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSchedules() async {
    if (!_permissionService.canScheduleReport()) {
      setState(() {
        _errorMessage = 'ليس لديك صلاحية لإدارة جدولة التقارير';
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل جميع الجدولات
      await _schedulesController.loadAllSchedules();
      _schedules = _schedulesController.allSchedules;

      // تصنيف الجدولات
      _activeSchedules = _schedules.where((s) => s.isActive).toList();
      _inactiveSchedules = _schedules.where((s) => !s.isActive).toList();
      _upcomingSchedules = _schedules.where((s) =>
        s.isActive &&
        DateTime.fromMillisecondsSinceEpoch(s.nextExecutionAt * 1000)
            .isAfter(DateTime.now()) &&
        DateTime.fromMillisecondsSinceEpoch(s.nextExecutionAt * 1000)
            .isBefore(DateTime.now().add(const Duration(days: 7)))
      ).toList();

    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadStatistics() async {
    try {
      // حساب الإحصائيات محلياً
      _statistics = {
        'totalSchedules': _schedules.length,
        'activeSchedules': _activeSchedules.length,
        'inactiveSchedules': _inactiveSchedules.length,
        'schedulesExecutedToday': 0, // يمكن حسابها لاحقاً
        'upcomingSchedules': _upcomingSchedules.length,
      };
      setState(() {});
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات الجدولة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة جدولة التقارير'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _loadSchedules();
              _loadStatistics();
            },
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showStatisticsDialog,
            tooltip: 'الإحصائيات',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_schedules',
                child: ListTile(
                  leading: Icon(Icons.file_download),
                  title: Text('تصدير الجدولات'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'import_schedules',
                child: ListTile(
                  leading: Icon(Icons.file_upload),
                  title: Text('استيراد الجدولات'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'bulk_operations',
                child: ListTile(
                  leading: Icon(Icons.checklist),
                  title: Text('عمليات مجمعة'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              text: 'الكل (${_schedules.length})',
              icon: const Icon(Icons.list),
            ),
            Tab(
              text: 'نشطة (${_activeSchedules.length})',
              icon: const Icon(Icons.play_circle),
            ),
            Tab(
              text: 'معطلة (${_inactiveSchedules.length})',
              icon: const Icon(Icons.pause_circle),
            ),
            Tab(
              text: 'قادمة (${_upcomingSchedules.length})',
              icon: const Icon(Icons.schedule),
            ),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: _permissionService.canScheduleReport()
          ? FloatingActionButton.extended(
              onPressed: _createNewSchedule,
              icon: const Icon(Icons.add_alarm),
              label: const Text('جدولة جديدة'),
              backgroundColor: AppColors.primary,
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل الجدولات...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل الجدولات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSchedules,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildSchedulesList(_schedules, 'لا توجد جدولات'),
        _buildSchedulesList(_activeSchedules, 'لا توجد جدولات نشطة'),
        _buildSchedulesList(_inactiveSchedules, 'لا توجد جدولات معطلة'),
        _buildUpcomingSchedulesList(),
      ],
    );
  }

  Widget _buildSchedulesList(List<ReportSchedule> schedules, String emptyMessage) {
    if (schedules.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.schedule_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: schedules.length,
      itemBuilder: (context, index) {
        final schedule = schedules[index];
        return _buildScheduleCard(schedule);
      },
    );
  }

  Widget _buildScheduleCard(ReportSchedule schedule) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: schedule.isActive ? Colors.green : Colors.grey,
          child: Icon(
            schedule.isActive ? Icons.play_arrow : Icons.pause,
            color: Colors.white,
          ),
        ),
        title: Text(
          schedule.title,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('التكرار: ${_getFrequencyDisplayName(schedule.frequency)}'),
            Text(
              'التنفيذ التالي: ${_formatDateTime(DateTime.fromMillisecondsSinceEpoch(schedule.nextExecutionAt * 1000))}',
              style: TextStyle(
                color: schedule.isActive ? Colors.blue : Colors.grey,
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleScheduleAction(value, schedule),
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'toggle',
              child: ListTile(
                leading: Icon(schedule.isActive ? Icons.pause : Icons.play_arrow),
                title: Text(schedule.isActive ? 'إيقاف' : 'تفعيل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'execute',
              child: ListTile(
                leading: Icon(Icons.play_circle_filled),
                title: Text('تنفيذ الآن'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'history',
              child: ListTile(
                leading: Icon(Icons.history),
                title: Text('سجل التنفيذ'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        onTap: () => _viewScheduleDetails(schedule),
      ),
    );
  }

  Widget _buildUpcomingSchedulesList() {
    if (_upcomingSchedules.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_available,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد جدولات قادمة في الأسبوع القادم',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    // ترتيب حسب وقت التنفيذ التالي
    final sortedSchedules = List<ReportSchedule>.from(_upcomingSchedules);
    sortedSchedules.sort((a, b) => a.nextExecutionAt.compareTo(b.nextExecutionAt));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sortedSchedules.length,
      itemBuilder: (context, index) {
        final schedule = sortedSchedules[index];
        final nextExecution = DateTime.fromMillisecondsSinceEpoch(schedule.nextExecutionAt * 1000);
        final timeUntil = nextExecution.difference(DateTime.now());

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getTimeUntilColor(timeUntil),
              child: Text(
                '${timeUntil.inDays}د',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(schedule.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(_formatDateTime(nextExecution)),
                Text(
                  _getTimeUntilText(timeUntil),
                  style: TextStyle(
                    color: _getTimeUntilColor(timeUntil),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            trailing: IconButton(
              icon: const Icon(Icons.play_circle_filled),
              onPressed: () => _executeScheduleNow(schedule),
              tooltip: 'تنفيذ الآن',
            ),
            onTap: () => _viewScheduleDetails(schedule),
          ),
        );
      },
    );
  }

  String _getFrequencyDisplayName(String frequency) {
    switch (frequency.toLowerCase()) {
      case 'daily':
        return 'يومي';
      case 'weekly':
        return 'أسبوعي';
      case 'monthly':
        return 'شهري';
      case 'quarterly':
        return 'ربع سنوي';
      case 'yearly':
        return 'سنوي';
      case 'custom':
        return 'مخصص';
      default:
        return frequency;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Color _getTimeUntilColor(Duration timeUntil) {
    if (timeUntil.inHours < 24) {
      return Colors.red;
    } else if (timeUntil.inDays < 3) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  String _getTimeUntilText(Duration timeUntil) {
    if (timeUntil.inDays > 0) {
      return 'خلال ${timeUntil.inDays} أيام';
    } else if (timeUntil.inHours > 0) {
      return 'خلال ${timeUntil.inHours} ساعات';
    } else {
      return 'خلال ${timeUntil.inMinutes} دقائق';
    }
  }

  void _createNewSchedule() {
    // TODO: فتح شاشة إنشاء جدولة جديدة
    Get.snackbar('قريباً', 'إنشاء الجدولات الجديدة قيد التطوير');
  }

  void _viewScheduleDetails(ReportSchedule schedule) {
    // TODO: فتح شاشة تفاصيل الجدولة
    Get.snackbar('قريباً', 'عرض تفاصيل الجدولة قيد التطوير');
  }

  void _handleScheduleAction(String action, ReportSchedule schedule) async {
    switch (action) {
      case 'toggle':
        await _toggleSchedule(schedule);
        break;
      case 'execute':
        await _executeScheduleNow(schedule);
        break;
      case 'edit':
        _editSchedule(schedule);
        break;
      case 'history':
        _viewExecutionHistory(schedule);
        break;
      case 'delete':
        _deleteSchedule(schedule);
        break;
    }
  }

  Future<void> _toggleSchedule(ReportSchedule schedule) async {
    try {
      final updatedSchedule = schedule.copyWith(isActive: !schedule.isActive);
      await _schedulesController.updateSchedule(schedule.id, updatedSchedule);
      _loadSchedules();
      Get.snackbar(
        'نجح',
        schedule.isActive ? 'تم إيقاف الجدولة' : 'تم تفعيل الجدولة',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تغيير حالة الجدولة: $e');
    }
  }

  Future<void> _executeScheduleNow(ReportSchedule schedule) async {
    try {
      // محاكاة تنفيذ الجدولة
      Get.snackbar(
        'نجح',
        'تم تنفيذ الجدولة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _loadSchedules();
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تنفيذ الجدولة: $e');
    }
  }

  void _editSchedule(ReportSchedule schedule) {
    // TODO: فتح شاشة تعديل الجدولة
    Get.snackbar('قريباً', 'تعديل الجدولات قيد التطوير');
  }

  void _viewExecutionHistory(ReportSchedule schedule) {
    // TODO: فتح شاشة سجل التنفيذ
    Get.snackbar('قريباً', 'سجل التنفيذ قيد التطوير');
  }

  void _deleteSchedule(ReportSchedule schedule) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الجدولة "${schedule.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              try {
                await _schedulesController.deleteSchedule(schedule.id);
                _loadSchedules();
                Get.snackbar('نجح', 'تم حذف الجدولة بنجاح');
              } catch (e) {
                Get.snackbar('خطأ', 'فشل في حذف الجدولة: $e');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showStatisticsDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('إحصائيات الجدولة'),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatisticRow('إجمالي الجدولات', _statistics['totalSchedules']?.toString() ?? '0'),
              _buildStatisticRow('الجدولات النشطة', _statistics['activeSchedules']?.toString() ?? '0'),
              _buildStatisticRow('الجدولات المعطلة', _statistics['inactiveSchedules']?.toString() ?? '0'),
              _buildStatisticRow('تم تنفيذها اليوم', _statistics['schedulesExecutedToday']?.toString() ?? '0'),
              _buildStatisticRow('قادمة هذا الأسبوع', _statistics['upcomingSchedules']?.toString() ?? '0'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export_schedules':
        // TODO: تصدير الجدولات
        Get.snackbar('قريباً', 'تصدير الجدولات قيد التطوير');
        break;
      case 'import_schedules':
        // TODO: استيراد الجدولات
        Get.snackbar('قريباً', 'استيراد الجدولات قيد التطوير');
        break;
      case 'bulk_operations':
        // TODO: العمليات المجمعة
        Get.snackbar('قريباً', 'العمليات المجمعة قيد التطوير');
        break;
    }
  }
}
