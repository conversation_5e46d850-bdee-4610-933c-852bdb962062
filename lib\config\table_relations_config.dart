/// تكوين العلاقات بين الجداول
/// 
/// يحتوي على تعريفات العلاقات المعروفة مسبقاً بين الجداول
/// لتسريع عملية اكتشاف العلاقات وتحسين دقة الربط التلقائي
class TableRelationsConfig {
  
  /// العلاقات المعرفة مسبقاً بين الجداول
  static const Map<String, List<TableRelation>> predefinedRelations = {
    'tasks': [
      TableRelation(
        targetTable: 'users',
        sourceColumn: 'assigneeId',
        targetColumn: 'id',
        relationshipType: RelationshipType.manyToOne,
        description: 'المهام المسندة للمستخدم',
        confidence: 0.95,
      ),
      TableRelation(
        targetTable: 'users',
        sourceColumn: 'creatorId', 
        targetColumn: 'id',
        relationshipType: RelationshipType.manyToOne,
        description: 'المهام التي أنشأها المستخدم',
        confidence: 0.95,
      ),
      TableRelation(
        targetTable: 'departments',
        sourceColumn: 'departmentId',
        targetColumn: 'id', 
        relationshipType: RelationshipType.manyToOne,
        description: 'المهام في القسم',
        confidence: 0.90,
      ),
      TableRelation(
        targetTable: 'task_types',
        sourceColumn: 'task_type_id',
        targetColumn: 'id',
        relationshipType: RelationshipType.manyToOne,
        description: 'نوع المهمة',
        confidence: 0.95,
      ),
      TableRelation(
        targetTable: 'task_statuses',
        sourceColumn: 'task_status_id',
        targetColumn: 'id',
        relationshipType: RelationshipType.manyToOne,
        description: 'حالة المهمة',
        confidence: 0.95,
      ),
      TableRelation(
        targetTable: 'task_priorities',
        sourceColumn: 'task_priority_id',
        targetColumn: 'id',
        relationshipType: RelationshipType.manyToOne,
        description: 'أولوية المهمة',
        confidence: 0.95,
      ),
    ],
    
    'users': [
      TableRelation(
        targetTable: 'departments',
        sourceColumn: 'departmentId',
        targetColumn: 'id',
        relationshipType: RelationshipType.manyToOne,
        description: 'المستخدمين في القسم',
        confidence: 0.90,
      ),
    ],
    
    'task_comments': [
      TableRelation(
        targetTable: 'tasks',
        sourceColumn: 'task_id',
        targetColumn: 'id',
        relationshipType: RelationshipType.manyToOne,
        description: 'تعليقات المهمة',
        confidence: 0.95,
      ),
      TableRelation(
        targetTable: 'users',
        sourceColumn: 'user_id',
        targetColumn: 'id',
        relationshipType: RelationshipType.manyToOne,
        description: 'المستخدم الذي كتب التعليق',
        confidence: 0.95,
      ),
    ],
    
    'task_attachments': [
      TableRelation(
        targetTable: 'tasks',
        sourceColumn: 'task_id',
        targetColumn: 'id',
        relationshipType: RelationshipType.manyToOne,
        description: 'مرفقات المهمة',
        confidence: 0.95,
      ),
      TableRelation(
        targetTable: 'users',
        sourceColumn: 'uploaded_by',
        targetColumn: 'id',
        relationshipType: RelationshipType.manyToOne,
        description: 'المستخدم الذي رفع المرفق',
        confidence: 0.90,
      ),
    ],
  };

  /// أنماط أسماء الأعمدة للمفاتيح الأجنبية
  static const List<ForeignKeyPattern> foreignKeyPatterns = [
    ForeignKeyPattern(
      pattern: r'(.+)_id$',
      targetTableExtractor: 1,
      targetColumn: 'id',
      confidence: 0.85,
    ),
    ForeignKeyPattern(
      pattern: r'(.+)Id$',
      targetTableExtractor: 1,
      targetColumn: 'id',
      confidence: 0.80,
    ),
    ForeignKeyPattern(
      pattern: r'^(.+)_ref$',
      targetTableExtractor: 1,
      targetColumn: 'id',
      confidence: 0.75,
    ),
  ];

  /// تطبيع أسماء الجداول
  static const Map<String, String> tableNameNormalizations = {
    'user': 'users',
    'department': 'departments',
    'task': 'tasks',
    'tasktype': 'task_types',
    'taskstatus': 'task_statuses',
    'taskpriority': 'task_priorities',
    'assignee': 'users',
    'creator': 'users',
    'comment': 'task_comments',
    'attachment': 'task_attachments',
  };

  /// الحصول على العلاقات المعرفة مسبقاً لجدول معين
  static List<TableRelation> getRelationsForTable(String tableName) {
    return predefinedRelations[tableName] ?? [];
  }

  /// البحث عن علاقة بين جدولين
  static TableRelation? findRelation(String sourceTable, String targetTable) {
    final relations = getRelationsForTable(sourceTable);
    
    for (final relation in relations) {
      if (relation.targetTable == targetTable) {
        return relation;
      }
    }
    
    return null;
  }

  /// تطبيع اسم الجدول
  static String normalizeTableName(String tableName) {
    return tableNameNormalizations[tableName.toLowerCase()] ?? tableName;
  }
}

/// تعريف علاقة بين جدولين
class TableRelation {
  final String targetTable;
  final String sourceColumn;
  final String targetColumn;
  final RelationshipType relationshipType;
  final String description;
  final double confidence;

  const TableRelation({
    required this.targetTable,
    required this.sourceColumn,
    required this.targetColumn,
    required this.relationshipType,
    required this.description,
    required this.confidence,
  });

  /// تحويل إلى Map للاستخدام في API
  Map<String, String> toMap(String sourceTable) {
    return {
      'table1': sourceTable,
      'column1': sourceColumn,
      'table2': targetTable,
      'column2': targetColumn,
      'joinCondition': '$sourceTable.$sourceColumn = $targetTable.$targetColumn',
      'joinType': 'LEFT JOIN',
      'description': description,
      'confidence': confidence.toString(),
      'relationshipType': relationshipType.name,
    };
  }
}

/// أنواع العلاقات
enum RelationshipType {
  oneToOne,
  oneToMany,
  manyToOne,
  manyToMany,
}

/// نمط للمفاتيح الأجنبية
class ForeignKeyPattern {
  final String pattern;
  final int targetTableExtractor;
  final String targetColumn;
  final double confidence;

  const ForeignKeyPattern({
    required this.pattern,
    required this.targetTableExtractor,
    required this.targetColumn,
    required this.confidence,
  });
}
