import 'dart:convert';
import 'package:flutter/foundation.dart';

import '../../models/report_models.dart';
import '../../professional_reports/models/report_template_models.dart';
import 'api_service.dart';

/// خدمة API لإدارة قوالب التقارير
class ReportTemplatesApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع القوالب
  Future<List<ReportTemplate>> getAllTemplates() async {
    try {
      final response = await _apiService.get('/api/ReportTemplates');
      final List<dynamic> data = _apiService.handleResponse<List<dynamic>>(
        response,
        (json) => json as List<dynamic>,
      );
      
      return data.map((item) => ReportTemplate.fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على القوالب: $e');
      rethrow;
    }
  }

  /// الحصول على قالب بالمعرف
  Future<ReportTemplate> getTemplateById(String id) async {
    try {
      final response = await _apiService.get('/api/ReportTemplates/$id');
      final Map<String, dynamic> data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
      
      return ReportTemplate.fromJson(data);
    } catch (e) {
      debugPrint('خطأ في الحصول على القالب $id: $e');
      rethrow;
    }
  }

  /// الحصول على القوالب الافتراضية
  Future<List<ReportTemplate>> getDefaultTemplates() async {
    try {
      final response = await _apiService.get('/api/ReportTemplates/default');
      final List<dynamic> data = _apiService.handleResponse<List<dynamic>>(
        response,
        (json) => json as List<dynamic>,
      );
      
      return data.map((item) => ReportTemplate.fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على القوالب الافتراضية: $e');
      rethrow;
    }
  }

  /// الحصول على القوالب حسب نوع التقرير
  Future<List<ReportTemplate>> getTemplatesByType(ReportType reportType) async {
    try {
      final response = await _apiService.get('/api/ReportTemplates/type/${reportType.value}');
      final List<dynamic> data = _apiService.handleResponse<List<dynamic>>(
        response,
        (json) => json as List<dynamic>,
      );
      
      return data.map((item) => ReportTemplate.fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على قوالب النوع ${reportType.value}: $e');
      rethrow;
    }
  }

  /// إنشاء قالب جديد
  Future<ReportTemplate> createTemplate(ReportTemplate template) async {
    try {
      final response = await _apiService.post('/api/ReportTemplates', template.toJson());
      final Map<String, dynamic> data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
      
      return ReportTemplate.fromJson(data);
    } catch (e) {
      debugPrint('خطأ في إنشاء القالب: $e');
      rethrow;
    }
  }

  /// تحديث قالب موجود
  Future<ReportTemplate> updateTemplate(ReportTemplate template) async {
    try {
      final response = await _apiService.put('/api/ReportTemplates/${template.id}', template.toJson());
      final Map<String, dynamic> data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
      
      return ReportTemplate.fromJson(data);
    } catch (e) {
      debugPrint('خطأ في تحديث القالب ${template.id}: $e');
      rethrow;
    }
  }

  /// حذف قالب
  Future<void> deleteTemplate(String id) async {
    try {
      final response = await _apiService.delete('/api/ReportTemplates/$id');
      _apiService.handleResponse<void>(response, (json) {});
    } catch (e) {
      debugPrint('خطأ في حذف القالب $id: $e');
      rethrow;
    }
  }

  /// نسخ قالب موجود
  Future<ReportTemplate> duplicateTemplate(String templateId, String newName) async {
    try {
      final response = await _apiService.post('/api/ReportTemplates/$templateId/duplicate', {
        'newName': newName,
      });
      final Map<String, dynamic> data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
      
      return ReportTemplate.fromJson(data);
    } catch (e) {
      debugPrint('خطأ في نسخ القالب $templateId: $e');
      rethrow;
    }
  }

  /// استعادة القوالب الافتراضية
  Future<void> resetDefaultTemplates() async {
    try {
      final response = await _apiService.post('/api/ReportTemplates/reset-defaults', {});
      _apiService.handleResponse<void>(response, (json) {});
    } catch (e) {
      debugPrint('خطأ في استعادة القوالب الافتراضية: $e');
      rethrow;
    }
  }

  /// تصدير قالب
  Future<String> exportTemplate(String templateId) async {
    try {
      final response = await _apiService.get('/api/ReportTemplates/$templateId/export');
      final Map<String, dynamic> data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
      
      return data['templateData'] as String;
    } catch (e) {
      debugPrint('خطأ في تصدير القالب $templateId: $e');
      rethrow;
    }
  }

  /// استيراد قالب
  Future<ReportTemplate> importTemplate(String templateData) async {
    try {
      final response = await _apiService.post('/api/ReportTemplates/import', {
        'templateData': templateData,
      });
      final Map<String, dynamic> data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
      
      return ReportTemplate.fromJson(data);
    } catch (e) {
      debugPrint('خطأ في استيراد القالب: $e');
      rethrow;
    }
  }

  /// إنشاء قالب من تقرير موجود
  Future<ReportTemplate> createTemplateFromReport(int reportId, String templateName) async {
    try {
      final response = await _apiService.post('/api/ReportTemplates/from-report', {
        'reportId': reportId,
        'templateName': templateName,
      });
      final Map<String, dynamic> data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
      
      return ReportTemplate.fromJson(data);
    } catch (e) {
      debugPrint('خطأ في إنشاء قالب من التقرير $reportId: $e');
      rethrow;
    }
  }

  /// البحث في القوالب
  Future<List<ReportTemplate>> searchTemplates({
    String? searchText,
    ReportType? reportType,
    bool? isDefault,
    bool? isCustom,
    int pageSize = 20,
    int pageNumber = 1,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'pageSize': pageSize.toString(),
        'pageNumber': pageNumber.toString(),
      };
      
      if (searchText != null && searchText.isNotEmpty) {
        queryParams['searchText'] = searchText;
      }
      
      if (reportType != null) {
        queryParams['reportType'] = reportType.value;
      }
      
      if (isDefault != null) {
        queryParams['isDefault'] = isDefault.toString();
      }
      
      if (isCustom != null) {
        queryParams['isCustom'] = isCustom.toString();
      }
      
      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');
      
      final response = await _apiService.get('/api/ReportTemplates/search?$queryString');
      final Map<String, dynamic> data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
      
      final List<dynamic> templates = data['templates'] as List<dynamic>;
      return templates.map((item) => ReportTemplate.fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في القوالب: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات القوالب
  Future<Map<String, dynamic>> getTemplateStatistics() async {
    try {
      final response = await _apiService.get('/api/ReportTemplates/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات القوالب: $e');
      rethrow;
    }
  }

  /// تطبيق قالب على تقرير
  Future<void> applyTemplateToReport(String templateId, int reportId) async {
    try {
      final response = await _apiService.post('/api/ReportTemplates/$templateId/apply', {
        'reportId': reportId,
      });
      _apiService.handleResponse<void>(response, (json) {});
    } catch (e) {
      debugPrint('خطأ في تطبيق القالب $templateId على التقرير $reportId: $e');
      rethrow;
    }
  }

  /// التحقق من صحة قالب
  Future<Map<String, dynamic>> validateTemplate(ReportTemplate template) async {
    try {
      final response = await _apiService.post('/api/ReportTemplates/validate', template.toJson());
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة القالب: $e');
      rethrow;
    }
  }

  /// معاينة قالب مع بيانات عينة
  Future<Map<String, dynamic>> previewTemplate(String templateId, Map<String, dynamic>? sampleData) async {
    try {
      final response = await _apiService.post('/api/ReportTemplates/$templateId/preview', {
        'sampleData': sampleData ?? {},
      });
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
    } catch (e) {
      debugPrint('خطأ في معاينة القالب $templateId: $e');
      rethrow;
    }
  }
}
