import 'package:flutter/material.dart';
import 'package:get/get.dart';


import '../../constants/app_colors.dart';
import '../../models/report_models.dart';
import '../../professional_reports/models/report_template_models.dart';
import '../../controllers/report_template_controller.dart';

import 'template_preview_screen.dart';

/// شاشة إدارة قوالب التقارير
class TemplateManagementScreen extends StatefulWidget {
  const TemplateManagementScreen({super.key});

  @override
  State<TemplateManagementScreen> createState() => _TemplateManagementScreenState();
}

class _TemplateManagementScreenState extends State<TemplateManagementScreen> {
  final ReportTemplateController _templateController = Get.find<ReportTemplateController>();

  
  List<ReportTemplate> _templates = [];
  List<ReportTemplate> _filteredTemplates = [];
  bool _isLoading = true;
  String _searchQuery = '';
  ReportType? _selectedType;
  bool _showDefaultOnly = false;
  bool _showCustomOnly = false;

  @override
  void initState() {
    super.initState();
    _loadTemplates();
  }

  Future<void> _loadTemplates() async {
    setState(() => _isLoading = true);
    
    try {
      _templates = await _templateController.getAllTemplates();
      _applyFilters();
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحميل القوالب: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _applyFilters() {
    _filteredTemplates = _templates.where((template) {
      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        if (!template.name.toLowerCase().contains(_searchQuery.toLowerCase()) &&
            !template.description.toLowerCase().contains(_searchQuery.toLowerCase())) {
          return false;
        }
      }
      
      // فلتر النوع
      if (_selectedType != null && template.reportType != _selectedType) {
        return false;
      }
      
      // فلتر القوالب الافتراضية
      if (_showDefaultOnly && !template.isDefault) {
        return false;
      }
      
      // فلتر القوالب المخصصة
      if (_showCustomOnly && !template.isCustom) {
        return false;
      }
      
      return true;
    }).toList();
    
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة قوالب التقارير'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
      
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showCreateTemplateDialog,
              tooltip: 'إنشاء قالب جديد',
            ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _importTemplate,
            tooltip: 'استيراد قالب',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_all',
                child: ListTile(
                  leading: Icon(Icons.file_upload),
                  title: Text('تصدير جميع القوالب'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'reset_defaults',
                child: ListTile(
                  leading: Icon(Icons.restore),
                  title: Text('استعادة القوالب الافتراضية'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildTemplateGrid(),
          ),
        ],
      ),
      floatingActionButton:  FloatingActionButton.extended(
              onPressed: _showTemplateWizard,
              icon: const Icon(Icons.auto_awesome),
              label: const Text('معالج القوالب'),
              backgroundColor: AppColors.primary,
            )
         
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            decoration: const InputDecoration(
              hintText: 'البحث في القوالب...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: (value) {
              _searchQuery = value;
              _applyFilters();
            },
          ),
          
          const SizedBox(height: 12),
          
          // فلاتر إضافية
          Row(
            children: [
              // فلتر النوع
              Expanded(
                child: DropdownButtonFormField<ReportType?>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    labelText: 'نوع التقرير',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    const DropdownMenuItem<ReportType?>(
                      value: null,
                      child: Text('جميع الأنواع'),
                    ),
                    ...ReportType.values.map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type.displayName),
                    )),
                  ],
                  onChanged: (value) {
                    _selectedType = value;
                    _applyFilters();
                  },
                ),
              ),
              
              const SizedBox(width: 12),
              
              // فلاتر سريعة
              FilterChip(
                label: const Text('افتراضي'),
                selected: _showDefaultOnly,
                onSelected: (selected) {
                  _showDefaultOnly = selected;
                  if (selected) _showCustomOnly = false;
                  _applyFilters();
                },
              ),
              
              const SizedBox(width: 8),
              
              FilterChip(
                label: const Text('مخصص'),
                selected: _showCustomOnly,
                onSelected: (selected) {
                  _showCustomOnly = selected;
                  if (selected) _showDefaultOnly = false;
                  _applyFilters();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateGrid() {
    if (_filteredTemplates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد قوالب متاحة',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'قم بإنشاء قالب جديد أو تعديل معايير البحث',
              style: TextStyle(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _filteredTemplates.length,
      itemBuilder: (context, index) {
        final template = _filteredTemplates[index];
        return _buildTemplateCard(template);
      },
    );
  }

  Widget _buildTemplateCard(ReportTemplate template) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معاينة القالب
          Container(
            height: 120,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              gradient: LinearGradient(
                colors: [
                  template.designSettings.primaryColor,
                  template.designSettings.secondaryColor,
                ],
              ),
            ),
            child: Center(
              child: Icon(
                template.reportType.icon,
                size: 48,
                color: Colors.white,
              ),
            ),
          ),
          
          // معلومات القالب
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    template.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    template.description,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Spacer(),
                  
                  // شارات القالب
                  Row(
                    children: [
                      if (template.isDefault)
                        _buildBadge('افتراضي', Colors.blue),
                      if (template.isCustom)
                        _buildBadge('مخصص', Colors.orange),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // أزرار الإجراءات
          OverflowBar(
            children: [
              TextButton(
                onPressed: () => _previewTemplate(template),
                child: const Text('معاينة'),
              ),
              TextButton(
                onPressed: () => _editTemplate(template),
                child: const Text('تعديل'),
              ),
              PopupMenuButton<String>(
                onSelected: (value) => _handleTemplateAction(value, template),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'duplicate',
                    child: ListTile(
                      leading: Icon(Icons.copy),
                      title: Text('نسخ'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'export',
                    child: ListTile(
                      leading: Icon(Icons.file_download),
                      title: Text('تصدير'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: Colors.red),
                      title: Text('حذف', style: TextStyle(color: Colors.red)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBadge(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      margin: const EdgeInsets.only(left: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _showCreateTemplateDialog() {
    // TODO: تنفيذ حوار إنشاء قالب جديد
    Get.snackbar('قريباً', 'ميزة إنشاء القوالب قيد التطوير');
  }

  void _importTemplate() {
    // TODO: تنفيذ استيراد القوالب
    Get.snackbar('قريباً', 'ميزة استيراد القوالب قيد التطوير');
  }

  void _showTemplateWizard() {
    // TODO: تنفيذ معالج القوالب
    Get.snackbar('قريباً', 'معالج القوالب قيد التطوير');
  }

  void _previewTemplate(ReportTemplate template) {
    Get.to(() => TemplatePreviewScreen(template: template));
  }

  void _editTemplate(ReportTemplate template) {
    // TODO: فتح شاشة تعديل القالب
    Get.snackbar('قريباً', 'تعديل القوالب قيد التطوير');
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export_all':
        // TODO: تصدير جميع القوالب
        Get.snackbar('قريباً', 'ميزة تصدير القوالب قيد التطوير');
        break;
      case 'reset_defaults':
        _resetDefaultTemplates();
        break;
    }
  }

  void _handleTemplateAction(String action, ReportTemplate template) {
    switch (action) {
      case 'duplicate':
        _duplicateTemplate(template);
        break;
      case 'export':
        _exportTemplate(template);
        break;
      case 'delete':
        _deleteTemplate(template);
        break;
    }
  }

  void _duplicateTemplate(ReportTemplate template) {
    // TODO: تنفيذ نسخ القالب
    Get.snackbar('قريباً', 'ميزة نسخ القوالب قيد التطوير');
  }

  void _exportTemplate(ReportTemplate template) {
    // TODO: تنفيذ تصدير القالب
    Get.snackbar('قريباً', 'ميزة تصدير القالب قيد التطوير');
  }

  void _deleteTemplate(ReportTemplate template) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القالب "${template.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              try {
                await _templateController.deleteTemplate(template.id);
                _loadTemplates();
                Get.snackbar('نجح', 'تم حذف القالب بنجاح');
              } catch (e) {
                Get.snackbar('خطأ', 'فشل في حذف القالب: $e');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _resetDefaultTemplates() {
    Get.dialog(
      AlertDialog(
        title: const Text('استعادة القوالب الافتراضية'),
        content: const Text('هل تريد استعادة جميع القوالب الافتراضية؟ سيتم استبدال أي تعديلات عليها.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              try {
                await _templateController.resetDefaultTemplates();
                _loadTemplates();
                Get.snackbar('نجح', 'تم استعادة القوالب الافتراضية بنجاح');
              } catch (e) {
                Get.snackbar('خطأ', 'فشل في استعادة القوالب: $e');
              }
            },
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }
}
