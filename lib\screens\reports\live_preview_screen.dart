import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';
import '../../constants/app_colors.dart';
import '../../models/report_models.dart';
import '../../models/reporting/report_result_model.dart';
import '../../controllers/reports_controller.dart';
import '../../services/unified_permission_service.dart';
import '../../screens/widgets/reporting/visualizations/enhanced_visualization_factory.dart';

/// شاشة المعاينة المباشرة للتقارير
class LivePreviewScreen extends StatefulWidget {
  final ReportType reportType;
  final Map<String, dynamic>? initialCriteria;
  final List<ReportVisualization>? initialVisualizations;

  const LivePreviewScreen({
    super.key,
    required this.reportType,
    this.initialCriteria,
    this.initialVisualizations,
  });

  @override
  State<LivePreviewScreen> createState() => _LivePreviewScreenState();
}

class _LivePreviewScreenState extends State<LivePreviewScreen> {
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  bool _isLoading = false;
  bool _autoRefresh = false;
  List<Map<String, dynamic>> _previewData = [];
  List<ReportVisualization> _visualizations = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _visualizations = widget.initialVisualizations ?? _getDefaultVisualizations();
    _loadPreviewData();
  }

  @override
  void dispose() {
    _autoRefresh = false;
    super.dispose();
  }

  List<ReportVisualization> _getDefaultVisualizations() {
    switch (widget.reportType) {
      case ReportType.taskStatus:
        return [
          ReportVisualization(
            id: 'task_status_pie',
            title: 'توزيع المهام حسب الحالة',
            type: VisualizationType.pieChart,
            dataFields: ['status', 'count'],
            showLegend: true,
          ),
          ReportVisualization(
            id: 'task_status_table',
            title: 'تفاصيل المهام',
            type: VisualizationType.table,
            dataFields: ['task_name', 'status', 'assignee', 'due_date'],
          ),
        ];
      case ReportType.userPerformance:
        return [
          ReportVisualization(
            id: 'user_performance_bar',
            title: 'أداء المستخدمين',
            type: VisualizationType.barChart,
            xAxisField: 'user_name',
            yAxisField: 'completed_tasks',
            dataFields: ['user_name', 'completed_tasks', 'total_tasks'],
            showValues: true,
          ),
        ];
      default:
        return [
          ReportVisualization(
            id: 'default_table',
            title: 'بيانات التقرير',
            type: VisualizationType.table,
            dataFields: [],
          ),
        ];
    }
  }

  Future<void> _loadPreviewData() async {
    if (!_permissionService.canViewReports()) {
      setState(() {
        _errorMessage = 'ليس لديك صلاحية لعرض التقارير';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(seconds: 1));
      
      // بيانات تجريبية للمعاينة
      _previewData = _generateSampleData();
      
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      setState(() => _isLoading = false);
    }
  }

  List<Map<String, dynamic>> _generateSampleData() {
    switch (widget.reportType) {
      case ReportType.taskStatus:
        return [
          {'status': 'مكتملة', 'count': 45, 'percentage': 60.0},
          {'status': 'قيد التنفيذ', 'count': 20, 'percentage': 26.7},
          {'status': 'معلقة', 'count': 10, 'percentage': 13.3},
        ];
      case ReportType.userPerformance:
        return [
          {'user_name': 'أحمد محمد', 'completed_tasks': 25, 'total_tasks': 30, 'completion_rate': 83.3},
          {'user_name': 'فاطمة علي', 'completed_tasks': 22, 'total_tasks': 25, 'completion_rate': 88.0},
          {'user_name': 'محمد سالم', 'completed_tasks': 18, 'total_tasks': 22, 'completion_rate': 81.8},
        ];
      default:
        return [
          {'item': 'عنصر 1', 'value': 100, 'category': 'فئة أ'},
          {'item': 'عنصر 2', 'value': 150, 'category': 'فئة ب'},
          {'item': 'عنصر 3', 'value': 75, 'category': 'فئة أ'},
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('معاينة مباشرة - ${widget.reportType.displayName}'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_autoRefresh ? Icons.pause : Icons.play_arrow),
            onPressed: _toggleAutoRefresh,
            tooltip: _autoRefresh ? 'إيقاف التحديث التلقائي' : 'تفعيل التحديث التلقائي',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPreviewData,
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
            tooltip: 'إعدادات المعاينة',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.file_download),
                  title: Text('تصدير المعاينة'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'save_template',
                child: ListTile(
                  leading: Icon(Icons.save),
                  title: Text('حفظ كقالب'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('مشاركة المعاينة'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildStatusBar(),
          Expanded(
            child: _buildPreviewContent(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createReportFromPreview,
        icon: const Icon(Icons.add_chart),
        label: const Text('إنشاء تقرير'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  Widget _buildStatusBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Icon(
            _isLoading ? Icons.hourglass_empty : Icons.check_circle,
            color: _isLoading ? Colors.orange : Colors.green,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            _isLoading ? 'جاري التحديث...' : 'محدث',
            style: TextStyle(
              color: _isLoading ? Colors.orange : Colors.green,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          if (_autoRefresh) ...[
            const Icon(Icons.autorenew, color: Colors.blue, size: 16),
            const SizedBox(width: 4),
            const Text(
              'تحديث تلقائي',
              style: TextStyle(color: Colors.blue, fontSize: 12),
            ),
            const SizedBox(width: 16),
          ],
          Text(
            'عدد السجلات: ${_previewData.length}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل المعاينة...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل المعاينة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPreviewData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_previewData.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.data_usage_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد بيانات للمعاينة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _visualizations.length,
      itemBuilder: (context, index) {
        final visualization = _visualizations[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      visualization.type.icon,
                      color: visualization.type.color,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        visualization.title,
                        style: AppStyles.titleMedium,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _editVisualization(index),
                      tooltip: 'تعديل التصور',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () => _removeVisualization(index),
                      tooltip: 'حذف التصور',
                    ),
                  ],
                ),
                const Divider(),
                SizedBox(
                  height: 300,
                  child: EnhancedVisualizationFactory.createVisualization(
                    visualization: visualization,
                    data: _previewData,
                    enableInteraction: true,
                    enableAnimation: true,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _toggleAutoRefresh() {
    setState(() {
      _autoRefresh = !_autoRefresh;
    });

    if (_autoRefresh) {
      _startAutoRefresh();
    }
  }

  void _startAutoRefresh() {
    if (!_autoRefresh) return;

    Future.delayed(const Duration(seconds: 30), () {
      if (_autoRefresh && mounted) {
        _loadPreviewData().then((_) => _startAutoRefresh());
      }
    });
  }

  void _showSettingsDialog() {
    // TODO: إظهار حوار إعدادات المعاينة
    Get.snackbar('قريباً', 'إعدادات المعاينة قيد التطوير');
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportPreview();
        break;
      case 'save_template':
        _saveAsTemplate();
        break;
      case 'share':
        _sharePreview();
        break;
    }
  }

  void _exportPreview() {
    // TODO: تصدير المعاينة
    Get.snackbar('قريباً', 'تصدير المعاينة قيد التطوير');
  }

  void _saveAsTemplate() {
    // TODO: حفظ المعاينة كقالب
    Get.snackbar('قريباً', 'حفظ القوالب قيد التطوير');
  }

  void _sharePreview() {
    // TODO: مشاركة المعاينة
    Get.snackbar('قريباً', 'مشاركة المعاينة قيد التطوير');
  }

  void _editVisualization(int index) {
    // TODO: تعديل التصور المرئي
    Get.snackbar('قريباً', 'تعديل التصورات قيد التطوير');
  }

  void _removeVisualization(int index) {
    setState(() {
      _visualizations.removeAt(index);
    });
  }

  void _createReportFromPreview() {
    // TODO: إنشاء تقرير من المعاينة
    Get.snackbar('قريباً', 'إنشاء التقارير من المعاينة قيد التطوير');
  }
}
