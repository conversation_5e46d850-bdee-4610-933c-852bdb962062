import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_styles.dart';
import '../../../constants/app_colors.dart';
import '../../../models/report_models.dart';
import '../../../services/advanced_export_service.dart';
import '../../../professional_reports/models/report_template_models.dart' as template_models;

/// حوار التصدير المتقدم
class AdvancedExportDialog extends StatefulWidget {
  final int? reportId;
  final ReportType? reportType;
  final List<Map<String, dynamic>>? customData;
  final String? defaultFileName;

  const AdvancedExportDialog({
    super.key,
    this.reportId,
    this.reportType,
    this.customData,
    this.defaultFileName,
  });

  @override
  State<AdvancedExportDialog> createState() => _AdvancedExportDialogState();
}

class _AdvancedExportDialogState extends State<AdvancedExportDialog> with TickerProviderStateMixin {
  final AdvancedExportService _exportService = AdvancedExportService();
  
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  
  // إعدادات أساسية
  ExportFormat _selectedFormat = ExportFormat.pdf;
  final TextEditingController _fileNameController = TextEditingController();
  
  // إعدادات المحتوى
  bool _includeCharts = true;
  bool _includeImages = true;
  bool _includeMetadata = true;
  bool _compressOutput = false;
  
  // إعدادات الأمان
  final TextEditingController _watermarkController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _enablePassword = false;
  
  // إعدادات التخصيص
  String _orientation = 'portrait';
  String _pageSize = 'A4';
  double _quality = 0.9;
  template_models.ReportTemplate? _selectedTemplate;
  final List<String> _selectedSections = [];
  
  // إعدادات ما بعد التصدير
  bool _openAfterExport = true;
  bool _shareAfterExport = false;
  
  // حالة التصدير
  bool _isExporting = false;
  double _exportProgress = 0.0;
  String _exportMessage = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _fileNameController.text = widget.defaultFileName ?? 'تقرير_${DateTime.now().millisecondsSinceEpoch}';
    
    // تحديد التنسيقات المدعومة
    if (widget.reportType != null) {
      final supportedFormats = _exportService.getSupportedFormats(widget.reportType!);
      if (supportedFormats.isNotEmpty && !supportedFormats.contains(_selectedFormat)) {
        _selectedFormat = supportedFormats.first;
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fileNameController.dispose();
    _watermarkController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _isExporting ? _buildExportProgress() : _buildTabContent(),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
      ),
      child: Row(
        children: [
          const Icon(Icons.file_download, color: Colors.white),
          const SizedBox(width: 8),
          const Text(
            'تصدير متقدم',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: () => Get.back(),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: Colors.grey,
          indicatorColor: AppColors.primary,
          tabs: const [
            Tab(text: 'أساسي', icon: Icon(Icons.settings)),
            Tab(text: 'المحتوى', icon: Icon(Icons.content_copy)),
            Tab(text: 'الأمان', icon: Icon(Icons.security)),
            Tab(text: 'متقدم', icon: Icon(Icons.tune)),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildBasicTab(),
              _buildContentTab(),
              _buildSecurityTab(),
              _buildAdvancedTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBasicTab() {
    final supportedFormats = widget.reportType != null 
        ? _exportService.getSupportedFormats(widget.reportType!)
        : ExportFormat.values;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تنسيق التصدير', style: AppStyles.titleMedium),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: supportedFormats.map((format) => ChoiceChip(
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(format.icon, size: 16, color: format.color),
                    const SizedBox(width: 4),
                    Text(format.displayName),
                  ],
                ),
                selected: _selectedFormat == format,
                onSelected: (selected) {
                  if (selected) {
                    setState(() => _selectedFormat = format);
                  }
                },
              )).toList(),
            ),
            
            const SizedBox(height: 24),
            
            Text('اسم الملف', style: AppStyles.titleMedium),
            const SizedBox(height: 8),
            TextFormField(
              controller: _fileNameController,
              decoration: InputDecoration(
                hintText: 'أدخل اسم الملف',
                suffixText: '.${_selectedFormat.value}',
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'اسم الملف مطلوب';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 24),
            
            Text('إعدادات ما بعد التصدير', style: AppStyles.titleMedium),
            const SizedBox(height: 8),
            CheckboxListTile(
              title: const Text('فتح الملف بعد التصدير'),
              subtitle: const Text('فتح الملف بالتطبيق الافتراضي'),
              value: _openAfterExport,
              onChanged: (value) => setState(() => _openAfterExport = value ?? false),
            ),
            CheckboxListTile(
              title: const Text('مشاركة الملف بعد التصدير'),
              subtitle: const Text('إظهار خيارات المشاركة'),
              value: _shareAfterExport,
              onChanged: (value) => setState(() => _shareAfterExport = value ?? false),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('عناصر المحتوى', style: AppStyles.titleMedium),
          const SizedBox(height: 16),
          
          CheckboxListTile(
            title: const Text('تضمين الرسوم البيانية'),
            subtitle: const Text('تصدير جميع المخططات والرسوم البيانية'),
            value: _includeCharts,
            onChanged: (value) => setState(() => _includeCharts = value ?? true),
          ),
          
          CheckboxListTile(
            title: const Text('تضمين الصور'),
            subtitle: const Text('تصدير الصور والشعارات'),
            value: _includeImages,
            onChanged: (value) => setState(() => _includeImages = value ?? true),
          ),
          
          CheckboxListTile(
            title: const Text('تضمين البيانات الوصفية'),
            subtitle: const Text('معلومات إضافية عن التقرير'),
            value: _includeMetadata,
            onChanged: (value) => setState(() => _includeMetadata = value ?? true),
          ),
          
          CheckboxListTile(
            title: const Text('ضغط الملف'),
            subtitle: const Text('تقليل حجم الملف المصدر'),
            value: _compressOutput,
            onChanged: (value) => setState(() => _compressOutput = value ?? false),
          ),
          
          const SizedBox(height: 24),
          
          if (_selectedFormat == ExportFormat.image) ...[
            Text('جودة الصورة', style: AppStyles.titleMedium),
            const SizedBox(height: 8),
            Slider(
              value: _quality,
              min: 0.1,
              max: 1.0,
              divisions: 9,
              label: '${(_quality * 100).round()}%',
              onChanged: (value) => setState(() => _quality = value),
            ),
            Text(
              'جودة: ${(_quality * 100).round()}%',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSecurityTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('العلامة المائية', style: AppStyles.titleMedium),
          const SizedBox(height: 8),
          TextFormField(
            controller: _watermarkController,
            decoration: const InputDecoration(
              hintText: 'نص العلامة المائية (اختياري)',
              border: OutlineInputBorder(),
            ),
          ),
          
          const SizedBox(height: 24),
          
          CheckboxListTile(
            title: const Text('حماية بكلمة مرور'),
            subtitle: const Text('تشفير الملف بكلمة مرور'),
            value: _enablePassword,
            onChanged: (value) => setState(() => _enablePassword = value ?? false),
          ),
          
          if (_enablePassword) ...[
            const SizedBox(height: 16),
            TextFormField(
              controller: _passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور',
                hintText: 'أدخل كلمة مرور قوية',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
              validator: (value) {
                if (_enablePassword && (value == null || value.length < 4)) {
                  return 'كلمة المرور يجب أن تكون 4 أحرف على الأقل';
                }
                return null;
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAdvancedTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_selectedFormat == ExportFormat.pdf) ...[
            Text('اتجاه الصفحة', style: AppStyles.titleMedium),
            const SizedBox(height: 8),
            SegmentedButton<String>(
              segments: const [
                ButtonSegment(value: 'portrait', label: Text('عمودي'), icon: Icon(Icons.portrait)),
                ButtonSegment(value: 'landscape', label: Text('أفقي'), icon: Icon(Icons.landscape)),
              ],
              selected: {_orientation},
              onSelectionChanged: (Set<String> selection) {
                setState(() => _orientation = selection.first);
              },
            ),
            
            const SizedBox(height: 24),
            
            Text('حجم الصفحة', style: AppStyles.titleMedium),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _pageSize,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'A4', child: Text('A4')),
                DropdownMenuItem(value: 'A3', child: Text('A3')),
                DropdownMenuItem(value: 'Letter', child: Text('Letter')),
                DropdownMenuItem(value: 'Legal', child: Text('Legal')),
              ],
              onChanged: (value) => setState(() => _pageSize = value ?? 'A4'),
            ),
          ],
          
          // يمكن إضافة المزيد من الإعدادات المتقدمة هنا
        ],
      ),
    );
  }

  Widget _buildExportProgress() {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            value: _exportProgress,
            strokeWidth: 6,
          ),
          const SizedBox(height: 24),
          Text(
            'جاري التصدير...',
            style: AppStyles.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _exportMessage,
            style: TextStyle(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            '${(_exportProgress * 100).round()}%',
            style: AppStyles.titleMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    if (_isExporting) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
              onPressed: () {
                // إلغاء التصدير
                setState(() => _isExporting = false);
              },
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: _startExport,
            icon: const Icon(Icons.file_download),
            label: const Text('تصدير'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _startExport() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isExporting = true;
      _exportProgress = 0.0;
      _exportMessage = 'بدء التصدير...';
    });

    try {
      final options = AdvancedExportOptions(
        format: _selectedFormat,
        fileName: _fileNameController.text,
        includeCharts: _includeCharts,
        includeImages: _includeImages,
        includeMetadata: _includeMetadata,
        compressOutput: _compressOutput,
        watermark: _watermarkController.text.isNotEmpty ? _watermarkController.text : null,
        password: _enablePassword ? _passwordController.text : null,
        orientation: _orientation,
        pageSize: _pageSize,
        quality: _quality,
        openAfterExport: _openAfterExport,
        shareAfterExport: _shareAfterExport,
      );

      ExportResult result;
      
      if (widget.reportId != null) {
        result = await _exportService.exportReport(
          reportId: widget.reportId!,
          options: options,
          onProgress: (progress, message) {
            setState(() {
              _exportProgress = progress;
              _exportMessage = message;
            });
          },
        );
      } else if (widget.customData != null) {
        result = await _exportService.exportCustomReport(
          data: widget.customData!,
          title: _fileNameController.text,
          options: options,
          onProgress: (progress, message) {
            setState(() {
              _exportProgress = progress;
              _exportMessage = message;
            });
          },
        );
      } else {
        throw Exception('لا توجد بيانات للتصدير');
      }

      if (result.success) {
        Get.back(result: result);
        Get.snackbar(
          'نجح التصدير',
          'تم تصدير الملف بنجاح: ${result.fileName}',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        setState(() => _isExporting = false);
        Get.snackbar(
          'فشل التصدير',
          result.errorMessage ?? 'حدث خطأ غير معروف',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      setState(() => _isExporting = false);
      Get.snackbar(
        'خطأ',
        'فشل في التصدير: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
